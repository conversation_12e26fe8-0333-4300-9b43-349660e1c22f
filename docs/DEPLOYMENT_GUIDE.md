# 📚 SaaS Image Upscaler 部署指南

## 🚀 部署前准备

### 1. 环境要求
- Node.js 18+ 
- pnpm (推荐) 或 npm
- Supabase 账户
- Stripe 账户
- Google Cloud Console 账户（用于OAuth）
- Cloudflare Workers 账户

### 2. 项目结构
```
image-upscaler/           # 前端项目
├── Frontend (Next.js)
new-iu-cf/               # 后端项目
├── Backend (Cloudflare Workers)
```

## 🔧 配置步骤

### 步骤 1: 数据库配置 (Supabase)

1. **创建 Supabase 项目**
   - 访问 [supabase.com](https://supabase.com)
   - 创建新项目
   - 记录 Project URL 和 anon key

2. **运行数据库迁移**
   ```bash
   cd /Users/<USER>/Desktop/yu/image-upscaler
   
   # 在 Supabase SQL Editor 中运行
   # 文件: data/migration-to-modern-schema.sql
   ```

3. **设置 RLS (Row Level Security)**
   ```sql
   -- 在 Supabase SQL Editor 中执行
   -- 文件已包含在 migration-to-modern-schema.sql 中
   ```

### 步骤 2: 前端配置 (Next.js)

1. **创建环境变量文件**
   ```bash
   cd /Users/<USER>/Desktop/yu/image-upscaler
   cp .env.example .env.local
   ```

2. **配置 .env.local 文件**
   ```env
   # NextAuth 配置
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-nextauth-secret-key-here
   
   # Google OAuth 配置
   NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
   NEXT_PUBLIC_AUTH_GOOGLE_ID=your-google-client-id
   AUTH_GOOGLE_ID=your-google-client-id
   AUTH_GOOGLE_SECRET=your-google-client-secret
   
   # Google One Tap (可选)
   NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=true
   
   # Supabase 配置
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   
   # Stripe 配置
   STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
   STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
   
   # 后端 API 配置
   NEXT_PUBLIC_BACKEND_API_URL=https://new-iu-cf.your-subdomain.workers.dev
   BACKEND_API_KEY=your-backend-api-key
   
   # 其他配置
   NEXT_PUBLIC_API_URL=/api
   ```

3. **安装依赖**
   ```bash
   pnpm install
   # 或
   npm install
   ```

### 步骤 3: Google OAuth 配置

1. **Google Cloud Console 设置**
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 创建新项目或选择现有项目
   - 启用 Google+ API
   - 创建 OAuth 2.0 客户端 ID

2. **OAuth 配置**
   ```
   应用类型: Web 应用
   授权重定向 URI: 
   - http://localhost:3000/api/auth/callback/google (开发环境)
   - https://yourdomain.com/api/auth/callback/google (生产环境)
   ```

3. **获取凭据**
   - 客户端 ID → `AUTH_GOOGLE_ID`
   - 客户端密钥 → `AUTH_GOOGLE_SECRET`

### 步骤 4: Stripe 配置

1. **Stripe Dashboard 设置**
   - 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
   - 获取 API 密钥 (测试环境)

2. **创建产品和价格**
   ```bash
   # 使用 Stripe CLI 创建产品
   stripe products create --name "Pro Plan" --description "500 monthly credits"
   stripe prices create --product prod_xxx --unit-amount 999 --currency usd --recurring interval=month
   stripe prices create --product prod_xxx --unit-amount 9999 --currency usd --recurring interval=year
   
   # 重复为 Explorer Plan
   stripe products create --name "Explorer Plan" --description "2000 monthly credits"
   stripe prices create --product prod_xxx --unit-amount 1999 --currency usd --recurring interval=month
   stripe prices create --product prod_xxx --unit-amount 19990 --currency usd --recurring interval=year
   ```

3. **更新价格 ID**
   ```typescript
   // 在 models/subscription.ts 中更新
   stripe_price_ids: {
     monthly: "price_your_actual_monthly_price_id",
     yearly: "price_your_actual_yearly_price_id"
   }
   ```

4. **设置 Webhook**
   ```
   端点 URL: https://yourdomain.com/api/stripe-notify
   事件类型:
   - customer.subscription.created
   - customer.subscription.updated
   - customer.subscription.deleted
   - invoice.payment_succeeded
   - invoice.payment_failed
   ```

### 步骤 5: 后端配置 (Cloudflare Workers)

1. **配置 wrangler.toml**
   ```toml
   # /Users/<USER>/Desktop/yu/new-iu-cf/worker/wrangler.toml
   name = "new-iu-cf"
   main = "src/index.ts"
   compatibility_date = "2023-12-01"
   
   [vars]
   BACKEND_API_KEY = "your-backend-api-key"
   FRONTEND_URL = "https://yourdomain.com"
   
   [[kv_namespaces]]
   binding = "CACHE"
   id = "your-kv-namespace-id"
   ```

2. **设置环境变量**
   ```bash
   cd /Users/<USER>/Desktop/yu/new-iu-cf/worker
   
   # 设置密钥
   wrangler secret put SUPABASE_SERVICE_ROLE_KEY
   wrangler secret put STRIPE_SECRET_KEY
   wrangler secret put EXTERNAL_API_KEY  # 用于 app-iu.a1d.ai
   ```

3. **安装依赖并部署**
   ```bash
   npm install
   npm run deploy
   ```

### 步骤 6: API 端点实现

创建必要的 API 端点文件:

1. **订阅管理 API**
   ```bash
   # 创建以下文件
   touch app/api/subscription/create/route.ts
   touch app/api/subscription/cancel/route.ts
   touch app/api/subscription/route.ts
   ```

2. **任务管理 API**
   ```bash
   touch app/api/tasks/route.ts
   touch app/api/tasks/[id]/route.ts
   ```

3. **文件上传 API**
   ```bash
   touch app/api/upload/route.ts
   ```

## 🧪 测试配置

### 本地开发测试

1. **启动前端开发服务器**
   ```bash
   cd /Users/<USER>/Desktop/yu/image-upscaler
   pnpm dev
   ```

2. **启动后端开发服务器**
   ```bash
   cd /Users/<USER>/Desktop/yu/new-iu-cf/worker
   npm run dev
   ```

3. **测试流程**
   - 访问 `http://localhost:3000`
   - 测试 Google 登录
   - 测试订阅升级流程
   - 测试图片上传和处理
   - 测试批量处理（Pro/Explorer）

### 生产环境部署

1. **前端部署 (Vercel 推荐)**
   ```bash
   # 安装 Vercel CLI
   npm i -g vercel
   
   # 部署
   cd /Users/<USER>/Desktop/yu/image-upscaler
   vercel --prod
   ```

2. **后端部署 (Cloudflare Workers)**
   ```bash
   cd /Users/<USER>/Desktop/yu/new-iu-cf/worker
   npm run deploy
   ```

## 🔍 验证清单

### 数据库验证
- [ ] Supabase 项目已创建
- [ ] 数据库迁移已成功运行
- [ ] RLS 策略已启用
- [ ] 数据库函数正常工作

### 认证验证
- [ ] Google OAuth 配置正确
- [ ] NextAuth 会话正常工作
- [ ] 用户信息正确存储
- [ ] 订阅信息正确加载

### 订阅验证
- [ ] Stripe 产品和价格已创建
- [ ] Webhook 端点已配置
- [ ] 订阅升级流程正常
- [ ] 信用点数正确分配

### 功能验证
- [ ] 单图片上传处理正常
- [ ] 批量处理功能正常（Pro/Explorer）
- [ ] 信用点数扣减正确
- [ ] 任务状态更新正常

## 🚨 故障排除

### 常见问题

1. **数据库连接错误**
   ```bash
   # 检查 Supabase 连接
   curl -H "apikey: YOUR_ANON_KEY" https://your-project.supabase.co/rest/v1/
   ```

2. **认证失败**
   ```bash
   # 检查 Google OAuth 配置
   # 确保重定向 URI 正确
   ```

3. **Stripe 支付失败**
   ```bash
   # 检查 Webhook 签名
   # 确保价格 ID 正确
   ```

4. **后端 API 错误**
   ```bash
   # 检查 Workers 日志
   wrangler tail
   ```

## 📧 支持

如果遇到问题：
1. 检查日志文件
2. 验证环境变量配置
3. 查看数据库连接状态
4. 确认 API 端点响应

## 🎯 下一步

配置完成后，您的 SaaS 图片放大平台将具备：
- 完整的用户认证系统
- 订阅管理功能
- 批量处理能力
- 信用点数管理
- 实时任务监控

祝您部署成功！ 🚀