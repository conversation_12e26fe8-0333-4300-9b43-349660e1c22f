# 🔌 API 端点实现指南

## 必需的 API 端点

### 1. 订阅管理 API

#### `/app/api/subscription/create/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { createSubscription } from '@/models/subscription';
import { stripe } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { planId, interval } = await request.json();
    
    // 创建 Stripe 结账会话
    const checkoutSession = await stripe.checkout.sessions.create({
      customer_email: session.user.email,
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price: getPriceId(planId, interval),
          quantity: 1,
        },
      ],
      success_url: `${process.env.NEXTAUTH_URL}/pay-success/{CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/pricing`,
      metadata: {
        userId: session.user.id,
        planId,
        interval,
      },
    });

    return NextResponse.json({ 
      sessionId: checkoutSession.id,
      url: checkoutSession.url 
    });

  } catch (error) {
    console.error('Subscription creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function getPriceId(planId: string, interval: string): string {
  const priceMap = {
    'pro': {
      monthly: 'price_pro_monthly_v2',
      yearly: 'price_pro_yearly_v2'
    },
    'explorer': {
      monthly: 'price_explorer_monthly_v2',
      yearly: 'price_explorer_yearly_v2'
    }
  };
  
  return priceMap[planId]?.[interval] || '';
}
```

#### `/app/api/subscription/cancel/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { cancelSubscription } from '@/models/subscription';
import { stripe } from '@/lib/stripe';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { subscriptionId } = await request.json();
    
    // 取消 Stripe 订阅
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    // 更新数据库
    await cancelSubscription(subscriptionId);

    return NextResponse.json({ 
      success: true,
      subscription 
    });

  } catch (error) {
    console.error('Subscription cancellation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

#### `/app/api/subscription/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { getUserSubscription } from '@/models/subscription';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const subscription = await getUserSubscription(session.user.id);
    
    return NextResponse.json({ subscription });

  } catch (error) {
    console.error('Get subscription error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### 2. 任务管理 API

#### `/app/api/tasks/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { createTask, getTasksByUser } from '@/models/task';
import { canUseCredits, updateUserCredits } from '@/services/credit';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    const tasks = await getTasksByUser(session.user.id, page, limit);
    
    return NextResponse.json({ tasks });

  } catch (error) {
    console.error('Get tasks error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { task_type, input_urls, scale, credits_cost } = data;

    // 检查用户是否有足够的信用点
    const hasCredits = await canUseCredits(session.user.id, credits_cost);
    if (!hasCredits) {
      return NextResponse.json({ error: 'Insufficient credits' }, { status: 400 });
    }

    // 创建任务
    const task = await createTask({
      user_id: session.user.id,
      task_type,
      input_urls,
      scale,
      credits_cost,
      metadata: data.metadata
    });

    // 扣除信用点
    await updateUserCredits(
      session.user.id,
      -credits_cost,
      'usage',
      `Image upscaling task: ${task.id}`,
      task.id
    );

    return NextResponse.json({ task });

  } catch (error) {
    console.error('Create task error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

#### `/app/api/tasks/[id]/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { getTaskById, updateTaskStatus } from '@/models/task';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const task = await getTaskById(params.id);
    
    if (!task || task.user_id !== session.user.id) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    return NextResponse.json({ task });

  } catch (error) {
    console.error('Get task error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { status, ...updates } = data;

    const task = await updateTaskStatus(params.id, status, updates);
    
    return NextResponse.json({ task });

  } catch (error) {
    console.error('Update task error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### 3. 文件上传 API

#### `/app/api/upload/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop();
    const fileName = `${session.user.id}/${Date.now()}.${fileExtension}`;

    // 上传到 S3
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_S3_BUCKET!,
      Key: fileName,
      Body: Buffer.from(await file.arrayBuffer()),
      ContentType: file.type,
    });

    await s3Client.send(command);

    const url = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${fileName}`;

    return NextResponse.json({ url });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
  }
}
```

### 4. 信用点数管理 API

#### `/app/api/credits/balance/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { getUserCreditsBalance } from '@/services/credit';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const balance = await getUserCreditsBalance(session.user.id);
    
    return NextResponse.json({ balance });

  } catch (error) {
    console.error('Get credits balance error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

#### `/app/api/credits/history/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth/config';
import { getUserCreditHistory } from '@/services/credit';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    const history = await getUserCreditHistory(session.user.id, page, limit);
    
    return NextResponse.json({ history });

  } catch (error) {
    console.error('Get credit history error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

### 5. Stripe Webhook 处理

#### `/app/api/stripe-notify/route.ts`
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { createSubscription, updateSubscription } from '@/models/subscription';
import { updateUserCredits } from '@/services/credit';

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const sig = request.headers.get('stripe-signature')!;

    let event;
    try {
      event = stripe.webhooks.constructEvent(body, sig, process.env.STRIPE_WEBHOOK_SECRET!);
    } catch (err) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 });
  }
}

async function handleSubscriptionCreated(subscription: any) {
  // 创建订阅记录
  await createSubscription({
    user_id: subscription.metadata.userId,
    stripe_subscription_id: subscription.id,
    stripe_customer_id: subscription.customer,
    stripe_price_id: subscription.items.data[0].price.id,
    plan_id: subscription.metadata.planId,
    plan_name: getplanName(subscription.metadata.planId),
    credits_included: getCreditsForPlan(subscription.metadata.planId),
    price_amount: subscription.items.data[0].price.unit_amount,
    price_currency: subscription.items.data[0].price.currency,
    billing_interval: subscription.items.data[0].price.recurring.interval,
    status: subscription.status,
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
  });

  // 分配信用点
  await updateUserCredits(
    subscription.metadata.userId,
    getCreditsForPlan(subscription.metadata.planId),
    'purchase',
    `Subscription created: ${subscription.metadata.planId}`
  );
}

async function handleSubscriptionUpdated(subscription: any) {
  // 更新订阅记录
  await updateSubscription(subscription.id, {
    status: subscription.status,
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    cancel_at_period_end: subscription.cancel_at_period_end,
    canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000).toISOString() : null,
  });
}

async function handleSubscriptionDeleted(subscription: any) {
  // 处理订阅删除
  await updateSubscription(subscription.id, {
    status: 'canceled',
    canceled_at: new Date().toISOString(),
  });
}

async function handlePaymentSucceeded(invoice: any) {
  // 处理付款成功
  if (invoice.billing_reason === 'subscription_cycle') {
    // 续费成功，分配月度信用点
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
    await updateUserCredits(
      subscription.metadata.userId,
      getCreditsForPlan(subscription.metadata.planId),
      'purchase',
      `Monthly credit allocation: ${subscription.metadata.planId}`
    );
  }
}

async function handlePaymentFailed(invoice: any) {
  // 处理付款失败
  console.error('Payment failed for invoice:', invoice.id);
}

function getplanName(planId: string): string {
  const nameMap = {
    'pro': 'Pro Plan',
    'explorer': 'Explorer Plan'
  };
  return nameMap[planId] || 'Unknown Plan';
}

function getCreditsForPlan(planId: string): number {
  const creditsMap = {
    'pro': 500,
    'explorer': 2000
  };
  return creditsMap[planId] || 0;
}
```

## 必需的配置文件

### `/lib/stripe.ts`
```typescript
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});
```

### `/lib/s3.ts` (如果使用 S3)
```typescript
import { S3Client } from '@aws-sdk/client-s3';

export const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});
```

## 创建文件命令

```bash
# 在 /Users/<USER>/Desktop/yu/image-upscaler 目录下运行

# 创建 API 目录结构
mkdir -p app/api/subscription/create
mkdir -p app/api/subscription/cancel
mkdir -p app/api/subscription
mkdir -p app/api/tasks
mkdir -p app/api/tasks/[id]
mkdir -p app/api/upload
mkdir -p app/api/credits/balance
mkdir -p app/api/credits/history
mkdir -p app/api/stripe-notify

# 创建必要的库文件
mkdir -p lib
touch lib/stripe.ts
touch lib/s3.ts

# 创建 API 文件
touch app/api/subscription/create/route.ts
touch app/api/subscription/cancel/route.ts
touch app/api/subscription/route.ts
touch app/api/tasks/route.ts
touch app/api/tasks/[id]/route.ts
touch app/api/upload/route.ts
touch app/api/credits/balance/route.ts
touch app/api/credits/history/route.ts
touch app/api/stripe-notify/route.ts
```

复制上面的代码到对应的文件中，然后根据您的具体需求调整配置。