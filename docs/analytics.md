# Vercel Analytics 集成

本项目已集成 Vercel Analytics，用于追踪用户行为和网站性能。

## 功能特性

### 1. 自动页面追踪
- Analytics 组件已添加到根布局 (`app/[locale]/layout.tsx`)
- 自动追踪页面浏览量和用户导航
- 无需额外配置即可工作

### 2. 自定义事件追踪
项目提供了 `AnalyticsUtils` 工具类 (`lib/analytics.ts`)，包含以下方法：

#### `trackEvent(name, properties?)`
追踪自定义事件
```typescript
AnalyticsUtils.trackEvent('button_click', { 
  location: 'header',
  button_type: 'cta' 
});
```

#### `trackInteraction(element, action, properties?)`
追踪用户交互
```typescript
AnalyticsUtils.trackInteraction('button', 'click', {
  location: 'homepage',
  section: 'hero'
});
```

#### `trackConversion(type, value?, properties?)`
追踪转化事件
```typescript
AnalyticsUtils.trackConversion('signup', undefined, {
  source: 'newsletter'
});
```

#### `trackPageView(page, properties?)`
手动追踪页面浏览（SPA 导航时使用）
```typescript
AnalyticsUtils.trackPageView('/dashboard', {
  user_type: 'premium'
});
```

## 使用示例

### 在组件中使用
```typescript
'use client';

import AnalyticsUtils from '@/lib/analytics';

export function MyComponent() {
  const handleClick = () => {
    AnalyticsUtils.trackInteraction('button', 'click', {
      component: 'MyComponent',
      action: 'submit_form'
    });
  };

  return (
    <button onClick={handleClick}>
      Submit
    </button>
  );
}
```

### 在 API 路由中使用
```typescript
// app/api/example/route.ts
import { track } from '@vercel/analytics/server';

export async function POST() {
  // 在服务端追踪事件
  track('api_call', { endpoint: 'example' });
  
  return Response.json({ success: true });
}
```

## 环境配置

### 开发环境
在开发环境中，Analytics 事件会在浏览器控制台中显示，但不会发送到 Vercel。

### 生产环境
部署到 Vercel 后，Analytics 会自动开始工作。你可以在 Vercel 仪表板中查看数据：
1. 登录 Vercel 仪表板
2. 选择你的项目
3. 点击 "Analytics" 标签

## 最佳实践

1. **事件命名**：使用清晰、一致的事件命名约定
2. **属性设置**：为事件添加有意义的属性，便于后续分析
3. **性能考虑**：避免过度追踪，专注于关键用户行为
4. **隐私合规**：确保追踪符合隐私政策和相关法规

## 数据查看

部署后，你可以在 Vercel 仪表板中查看：
- 页面浏览量
- 用户会话
- 自定义事件
- 性能指标
- 用户地理分布

## 注意事项

- Analytics 只在客户端组件中工作（需要 `'use client'` 指令）
- 服务端追踪需要使用 `@vercel/analytics/server`
- 开发环境中的事件不会出现在仪表板中
- 确保项目部署在 Vercel 平台上才能看到数据 