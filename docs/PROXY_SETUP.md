# 代理配置指南

本项目支持在开发环境中使用代理，特别适用于需要访问 Google OAuth 等被墙服务的情况。

## 快速开始

### 方式1：使用 npm 脚本（推荐）
```bash
pnpm dev:proxy
```

### 方式2：使用 shell 脚本
```bash
./scripts/dev-with-proxy.sh
```

### 方式3：手动设置环境变量
```bash
export PROXY_ENABLED=true
export HTTPS_PROXY=http://127.0.0.1:7890
export HTTP_PROXY=http://127.0.0.1:7890
export ALL_PROXY=socks5://127.0.0.1:7890
pnpm dev
```

## 代理配置

### 环境变量
在 `.env.local` 文件中添加以下配置：

```env
# 启用代理（开发环境默认启用）
PROXY_ENABLED=true

# HTTP 代理
HTTP_PROXY=http://127.0.0.1:7890

# HTTPS 代理
HTTPS_PROXY=http://127.0.0.1:7890

# SOCKS 代理
ALL_PROXY=socks5://127.0.0.1:7890
```

### 默认代理地址
- HTTP/HTTPS: `http://127.0.0.1:7890`
- SOCKS5: `socks5://127.0.0.1:7890`

这些是常见代理软件（如 Clash、V2Ray）的默认端口。

## 支持的功能

✅ NextAuth Google OAuth 登录  
✅ NextAuth GitHub OAuth 登录  
✅ Google One Tap 登录  
✅ 后端 API 请求  
✅ 第三方服务调用  

## 技术实现

项目使用以下代理库：
- `https-proxy-agent`: HTTPS 请求代理
- `http-proxy-agent`: HTTP 请求代理  
- `socks-proxy-agent`: SOCKS 代理支持

代理在以下层面生效：
1. **NextAuth 层面**: OAuth 提供商通信
2. **Fetch 层面**: 所有 HTTP 请求
3. **API 客户端层面**: 后端服务通信

## 故障排除

### 代理不生效
1. 确认代理软件正在运行
2. 检查代理端口是否正确
3. 查看控制台是否有代理配置日志

### Google 登录失败
1. 确认 Google OAuth 配置正确
2. 检查代理是否能访问 `oauth2.googleapis.com`
3. 查看浏览器网络面板的错误信息

### 环境变量不生效
1. 重启开发服务器
2. 确认 `.env.local` 文件格式正确
3. 检查环境变量是否被正确读取

## 生产环境

代理配置仅在开发环境生效，生产环境会自动禁用代理功能。
