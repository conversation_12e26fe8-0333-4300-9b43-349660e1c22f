# 🚀 快速开始指南

## 1. 最短路径部署

### 前置条件
- Node.js 18+
- pnpm 或 npm
- 已有 Supabase、Stripe、Google Cloud 账户

### 🏃‍♂️ 5分钟快速启动

```bash
# 1. 配置环境变量
cd /Users/<USER>/Desktop/yu/image-upscaler
cp env.example .env.local

# 2. 安装依赖
pnpm install

# 3. 启动开发服务器
pnpm dev

# 4. 启动后端服务
cd /Users/<USER>/Desktop/yu/new-iu-cf/worker
npm run dev
```

### 📝 必填环境变量（最小配置）

```env
# .env.local
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key

# 后端
NEXT_PUBLIC_BACKEND_API_URL=http://localhost:8787
```

## 2. 配置检查清单

### ✅ 必需配置

| 服务 | 状态 | 说明 |
|------|------|------|
| 🗄️ Supabase | ⭕ 必需 | 数据库和认证 |
| 💳 Stripe | ⭕ 必需 | 支付和订阅 |
| 🔐 Google OAuth | ⭕ 必需 | 用户登录 |
| ☁️ Cloudflare Workers | ⭕ 必需 | 后端 API |

### 🔧 配置优先级

1. **P0 (立即需要)**
   - Supabase 数据库迁移
   - Google OAuth 配置
   - 基础环境变量

2. **P1 (功能需要)**
   - Stripe 支付配置
   - 后端 API 部署

3. **P2 (增强功能)**
   - 文件存储 (S3/CDN)
   - 监控和分析

## 3. 分步骤配置

### 步骤 1: 数据库设置 (5分钟)

1. **创建 Supabase 项目**
   - 访问 [supabase.com](https://supabase.com)
   - 创建新项目
   - 获取 Project URL 和 service_role key

2. **运行数据库迁移**
   ```sql
   -- 在 Supabase SQL Editor 中运行
   -- 复制 data/migration-to-modern-schema.sql 的内容
   ```

3. **验证数据库**
   ```bash
   # 测试连接
   curl -H "apikey: YOUR_SERVICE_ROLE_KEY" \
        -H "Authorization: Bearer YOUR_SERVICE_ROLE_KEY" \
        https://your-project.supabase.co/rest/v1/users
   ```

### 步骤 2: 认证设置 (5分钟)

1. **Google Cloud Console**
   - 创建 OAuth 2.0 客户端
   - 设置重定向 URI: `http://localhost:3000/api/auth/callback/google`

2. **测试登录**
   ```bash
   # 启动开发服务器
   pnpm dev
   
   # 访问 http://localhost:3000
   # 点击 Google 登录
   ```

### 步骤 3: 支付设置 (10分钟)

1. **Stripe 配置**
   ```bash
   # 创建测试产品
   stripe products create --name "Pro Plan"
   stripe prices create --product prod_xxx --unit-amount 999 --currency usd --recurring interval=month
   ```

2. **更新价格 ID**
   ```typescript
   // models/subscription.ts
   stripe_price_id: 'price_YOUR_ACTUAL_PRICE_ID'
   ```

### 步骤 4: 后端部署 (5分钟)

1. **配置 Cloudflare Workers**
   ```bash
   cd /Users/<USER>/Desktop/yu/new-iu-cf/worker
   npm install
   npm run deploy
   ```

2. **更新后端 URL**
   ```env
   NEXT_PUBLIC_BACKEND_API_URL=https://your-worker.your-subdomain.workers.dev
   ```

## 4. 验证测试

### 🧪 功能测试清单

```bash
# 1. 认证测试
curl -X GET http://localhost:3000/api/auth/session

# 2. 数据库测试
curl -X GET http://localhost:3000/api/subscription

# 3. 支付测试
curl -X POST http://localhost:3000/api/subscription/create \
  -H "Content-Type: application/json" \
  -d '{"planId": "pro", "interval": "monthly"}'

# 4. 后端 API 测试
curl -X GET https://your-worker.your-subdomain.workers.dev/health
```

### 🎯 测试场景

1. **用户流程**
   - ✅ Google 登录
   - ✅ 查看订阅状态
   - ✅ 升级到 Pro
   - ✅ 使用批量处理

2. **支付流程**
   - ✅ 创建订阅
   - ✅ 支付成功
   - ✅ Webhook 处理
   - ✅ 信用点分配

## 5. 常见问题快速解决

### ❌ 登录失败
```bash
# 检查 Google OAuth 配置
echo "Client ID: $AUTH_GOOGLE_ID"
echo "Redirect URI: http://localhost:3000/api/auth/callback/google"
```

### ❌ 数据库连接失败
```bash
# 检查 Supabase 配置
echo "URL: $SUPABASE_URL"
echo "Key: ${SUPABASE_SERVICE_ROLE_KEY:0:20}..."
```

### ❌ 支付失败
```bash
# 检查 Stripe 配置
echo "Publishable: ${STRIPE_PUBLISHABLE_KEY:0:20}..."
echo "Secret: ${STRIPE_SECRET_KEY:0:20}..."
```

### ❌ 后端 API 失败
```bash
# 检查 Workers 状态
curl -X GET https://your-worker.your-subdomain.workers.dev/health
```

## 6. 生产环境部署

### 🚀 一键部署

```bash
# 前端部署 (Vercel)
npx vercel --prod

# 后端部署 (Cloudflare)
cd /Users/<USER>/Desktop/yu/new-iu-cf/worker
npm run deploy
```

### 🔧 生产环境变量

```env
# 更新为生产环境值
NEXTAUTH_URL=https://yourdomain.com
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...
NEXT_PUBLIC_BACKEND_API_URL=https://your-worker.your-subdomain.workers.dev
```

## 7. 监控和维护

### 📊 关键指标

- 用户注册数
- 订阅转换率
- 任务处理成功率
- 支付成功率

### 🔍 日志检查

```bash
# 前端日志
vercel logs

# 后端日志
wrangler tail

# 数据库日志
# Supabase Dashboard → Logs
```

## 8. 支持资源

### 📚 文档链接

- [完整部署指南](./DEPLOYMENT_GUIDE.md)
- [Stripe 配置详解](./STRIPE_SETUP.md)
- [API 实现指南](./API_IMPLEMENTATION.md)
- [实现进度追踪](./IMPLEMENTATION_PROGRESS.md)

### 🆘 获取帮助

如果遇到问题：
1. 查看错误日志
2. 检查环境变量
3. 验证服务状态
4. 参考故障排除指南

---

## 🎉 完成！

配置完成后，您将拥有一个功能完整的 SaaS 图片放大平台：

- ✅ 用户认证和管理
- ✅ 订阅和支付系统
- ✅ 图片处理和批量功能
- ✅ 信用点数管理
- ✅ 实时任务监控

开始使用吧！ 🚀