# 💳 Stripe 配置详细指南

## 🚀 Stripe 账户设置

### 1. 创建 Stripe 账户
1. 访问 [https://dashboard.stripe.com/register](https://dashboard.stripe.com/register)
2. 注册并验证您的账户
3. 完成商家信息填写

### 2. 获取 API 密钥
1. 进入 Stripe Dashboard
2. 点击 "Developers" → "API keys"
3. 复制以下密钥：
   - **Publishable key** (pk_test_...) → `STRIPE_PUBLISHABLE_KEY`
   - **Secret key** (sk_test_...) → `STRIPE_SECRET_KEY`

## 🛍️ 创建产品和价格

### 方法 1: 通过 Stripe CLI (推荐)

1. **安装 Stripe CLI**
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe
   
   # 或下载二进制文件
   # https://github.com/stripe/stripe-cli/releases
   ```

2. **登录 Stripe CLI**
   ```bash
   stripe login
   ```

3. **创建产品和价格**
   ```bash
   # 创建 Pro Plan 产品
   stripe products create \
     --name "Pro Plan" \
     --description "Perfect for content creators and professionals" \
     --metadata plan_id=pro \
     --metadata credits=500
   
   # 记录返回的产品 ID (prod_xxx)
   
   # 创建 Pro Plan 月度价格
   stripe prices create \
     --product prod_YOUR_PRO_PRODUCT_ID \
     --unit-amount 999 \
     --currency usd \
     --recurring interval=month \
     --metadata plan_id=pro \
     --metadata interval=monthly
   
   # 创建 Pro Plan 年度价格
   stripe prices create \
     --product prod_YOUR_PRO_PRODUCT_ID \
     --unit-amount 9999 \
     --currency usd \
     --recurring interval=year \
     --metadata plan_id=pro \
     --metadata interval=yearly
   
   # 创建 Explorer Plan 产品
   stripe products create \
     --name "Explorer Plan" \
     --description "For heavy users and small teams" \
     --metadata plan_id=explorer \
     --metadata credits=2000
   
   # 记录返回的产品 ID (prod_xxx)
   
   # 创建 Explorer Plan 月度价格
   stripe prices create \
     --product prod_YOUR_EXPLORER_PRODUCT_ID \
     --unit-amount 1999 \
     --currency usd \
     --recurring interval=month \
     --metadata plan_id=explorer \
     --metadata interval=monthly
   
   # 创建 Explorer Plan 年度价格
   stripe prices create \
     --product prod_YOUR_EXPLORER_PRODUCT_ID \
     --unit-amount 19990 \
     --currency usd \
     --recurring interval=year \
     --metadata plan_id=explorer \
     --metadata interval=yearly
   ```

### 方法 2: 通过 Stripe Dashboard

1. **创建产品**
   - 进入 Stripe Dashboard → Products
   - 点击 "Add product"
   - 填写产品信息：
     - Name: "Pro Plan"
     - Description: "Perfect for content creators and professionals"
     - Metadata: `plan_id: pro`, `credits: 500`

2. **创建价格**
   - 在产品页面点击 "Add price"
   - 设置价格：
     - Price: $9.99 (monthly), $99.99 (yearly)
     - Billing period: Monthly/Yearly
     - Metadata: `plan_id: pro`, `interval: monthly/yearly`

## 🔄 更新价格 ID

创建完产品和价格后，更新以下文件中的价格 ID：

### `/models/subscription.ts`
```typescript
export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlan> = {
  pro: {
    id: 'pro',
    name: 'Pro Plan',
    description: 'Perfect for content creators and professionals',
    pricing: {
      monthly: {
        amount: 999, // $9.99 in cents
        currency: 'USD',
        stripe_price_id: 'price_YOUR_ACTUAL_PRO_MONTHLY_PRICE_ID' // 替换这里
      },
      yearly: {
        amount: 9999, // $99.99 in cents
        currency: 'USD',
        stripe_price_id: 'price_YOUR_ACTUAL_PRO_YEARLY_PRICE_ID', // 替换这里
        discount_percentage: 17
      }
    },
    // ... 其他配置
  },
  explorer: {
    id: 'explorer',
    name: 'Explorer Plan',
    description: 'For heavy users and small teams',
    pricing: {
      monthly: {
        amount: 1999, // $19.99 in cents
        currency: 'USD',
        stripe_price_id: 'price_YOUR_ACTUAL_EXPLORER_MONTHLY_PRICE_ID' // 替换这里
      },
      yearly: {
        amount: 19990, // $199.90 in cents
        currency: 'USD',
        stripe_price_id: 'price_YOUR_ACTUAL_EXPLORER_YEARLY_PRICE_ID', // 替换这里
        discount_percentage: 17
      }
    },
    // ... 其他配置
  }
};
```

## 🔗 设置 Webhooks

### 1. 创建 Webhook 端点

1. **进入 Stripe Dashboard**
   - Developers → Webhooks
   - 点击 "Add endpoint"

2. **配置端点**
   ```
   Endpoint URL: https://yourdomain.com/api/stripe-notify
   Description: Image Upscaler Subscription Webhooks
   ```

3. **选择事件类型**
   ```
   customer.subscription.created
   customer.subscription.updated
   customer.subscription.deleted
   invoice.payment_succeeded
   invoice.payment_failed
   checkout.session.completed
   ```

4. **获取 Webhook 签名密钥**
   - 创建后点击 webhook 端点
   - 复制 "Signing secret" (whsec_...)
   - 添加到环境变量: `STRIPE_WEBHOOK_SECRET`

### 2. 本地测试 Webhooks

```bash
# 安装 Stripe CLI 后
stripe listen --forward-to localhost:3000/api/stripe-notify

# 在新终端中测试
stripe trigger customer.subscription.created
```

## 📊 测试订阅流程

### 1. 测试卡号

```
成功付款: 4242 4242 4242 4242
需要 3DS 验证: 4000 0027 6000 3184
被拒绝: 4000 0000 0000 0002
余额不足: 4000 0000 0000 9995
```

### 2. 测试流程

1. **创建订阅**
   ```bash
   curl -X POST http://localhost:3000/api/subscription/create \
     -H "Content-Type: application/json" \
     -d '{
       "planId": "pro",
       "interval": "monthly"
     }'
   ```

2. **完成支付**
   - 使用返回的 Stripe Checkout URL
   - 使用测试卡号完成支付

3. **验证 Webhook**
   - 检查控制台日志
   - 验证数据库中的订阅记录
   - 确认信用点数已分配

## 🔧 生产环境配置

### 1. 切换到生产模式

1. **获取生产密钥**
   - 在 Stripe Dashboard 中切换到 "Live mode"
   - 获取生产环境的 API 密钥

2. **更新环境变量**
   ```env
   STRIPE_PUBLISHABLE_KEY=pk_live_...
   STRIPE_SECRET_KEY=sk_live_...
   STRIPE_WEBHOOK_SECRET=whsec_live_...
   ```

### 2. 创建生产 Webhooks

1. **创建新的 Webhook 端点**
   ```
   Endpoint URL: https://yourdomain.com/api/stripe-notify
   ```

2. **配置相同的事件类型**

## 📈 监控和分析

### 1. Stripe Dashboard 监控

- **订阅概览**: Dashboard → Subscriptions
- **付款记录**: Dashboard → Payments
- **Webhook 日志**: Developers → Webhooks → [端点] → Attempts

### 2. 自定义监控

```typescript
// 在 webhook 处理中添加监控
console.log('Webhook received:', {
  type: event.type,
  subscription_id: event.data.object.id,
  user_id: event.data.object.metadata?.userId,
  timestamp: new Date().toISOString()
});
```

## 🚨 故障排除

### 常见问题

1. **Webhook 签名验证失败**
   ```bash
   # 检查签名密钥
   echo $STRIPE_WEBHOOK_SECRET
   
   # 验证端点 URL
   stripe listen --forward-to localhost:3000/api/stripe-notify
   ```

2. **订阅状态不同步**
   ```bash
   # 手动触发 webhook
   stripe trigger customer.subscription.updated
   ```

3. **价格 ID 错误**
   ```bash
   # 列出所有价格
   stripe prices list
   ```

### 调试命令

```bash
# 查看所有产品
stripe products list

# 查看特定产品的价格
stripe prices list --product prod_YOUR_PRODUCT_ID

# 查看订阅详情
stripe subscriptions retrieve sub_YOUR_SUBSCRIPTION_ID

# 查看 webhook 事件
stripe events list --type customer.subscription.created
```

## 🎯 完成检查清单

- [ ] Stripe 账户已创建并验证
- [ ] API 密钥已获取并配置
- [ ] 产品和价格已创建
- [ ] 价格 ID 已更新到代码中
- [ ] Webhook 端点已配置
- [ ] Webhook 签名密钥已设置
- [ ] 测试订阅流程正常
- [ ] 信用点数分配正确
- [ ] 生产环境配置完成

完成这些步骤后，您的 Stripe 支付系统就配置完成了！