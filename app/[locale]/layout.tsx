import "@/app/globals.css";

import { getMessages, getTranslations } from "next-intl/server";

import { AppContextProvider } from "@/contexts/app";
import { Inter as FontSans } from "next/font/google";
import { Metadata } from "next";
import { NextAuthSessionProvider } from "@/auth/session";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/providers/theme";
import { Analytics } from "@vercel/analytics/react";
import { StructuredData, OrganizationStructuredData } from "@/components/seo/structured-data";
import { cn } from "@/lib/utils";
import { setupGlobalProxy } from "@/lib/proxy";

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
});

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}): Promise<Metadata> {
  const t = await getTranslations();

  return {
    title: {
      template: `%s | ${t("metadata.title")}`,
      default: t("metadata.title") || "",
    },
    description: t("metadata.description") || "",
    keywords: t("metadata.keywords") || "",
    authors: [{ name: "ImageUpscaler Team" }],
    creator: "ImageUpscaler",
    publisher: "ImageUpscaler",
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: 'website',
      locale: locale,
      url: 'https://imageupscaler.run',
      siteName: 'ImageUpscaler',
      title: t("metadata.title") || "",
      description: t("metadata.description") || "",
      images: [
        {
          url: '/og-image.png',
          width: 1200,
          height: 630,
          alt: 'ImageUpscaler - AI Image Enhancement Tool',
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: '@imageupscaler',
      creator: '@imageupscaler',
      title: t("metadata.title") || "",
      description: t("metadata.description") || "",
      images: ['/twitter-image.png'],
    },
    icons: {
      icon: '/favicon.ico',
      shortcut: '/favicon-16x16.png',
      apple: '/apple-touch-icon.png',
    },
    manifest: '/site.webmanifest',
    alternates: {
      canonical: 'https://imageupscaler.run',
      languages: {
        'en': 'https://imageupscaler.run/en',
        'zh': 'https://imageupscaler.run/zh',
      },
    },
    category: 'technology',
  };
}

export default async function RootLayout({
  children,
  params: { locale },
}: Readonly<{
  children: React.ReactNode;
  params: { locale: string };
}>) {
  // 在开发环境中设置代理
  if (process.env.NODE_ENV === 'development') {
    setupGlobalProxy();
  }

  const messages = await getMessages();

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <StructuredData />
        <OrganizationStructuredData />
      </head>
      <body
        className={cn(
          "min-h-screen bg-background font-sans antialiased overflow-x-hidden",
          fontSans.variable
        )}
      >
        <NextIntlClientProvider messages={messages}>
          <NextAuthSessionProvider>
            <AppContextProvider>
              <ThemeProvider attribute="class" disableTransitionOnChange>
                {children}
              </ThemeProvider>
            </AppContextProvider>
          </NextAuthSessionProvider>
        </NextIntlClientProvider>
        <Analytics />
      </body>
    </html>
  );
}
