import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { BatchUpscale } from "@/components/features/batch-upscale";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations();
  
  return {
    title: "Batch Image Upscaling - Process Multiple Images | ImageUpscaler",
    description: "Upload and process multiple images simultaneously with our AI-powered batch upscaling feature. Perfect for Pro and Explorer users.",
    keywords: "batch image upscaling, multiple image processing, AI batch enhancement, bulk photo upscaling"
  };
}

export default async function BatchUpscalePage() {
  const t = await getTranslations();

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8">
        <BatchUpscale />
      </div>
    </div>
  );
}
