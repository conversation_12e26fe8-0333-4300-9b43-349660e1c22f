import { Suspense } from "react";
import { getTranslations } from "next-intl/server";
import { getUserUuid } from "@/services/user";
import { getUserSubscriptionInfo } from "@/models/subscription";
import { getUserCredits } from "@/services/credit";
import Empty from "@/components/blocks/empty";
import { SubscriptionDashboard } from "@/components/console/subscription-dashboard";

export default async function SubscriptionPage() {
  const t = await getTranslations();
  const userUuid = await getUserUuid();

  if (!userUuid) {
    return <Empty message="Authentication required" />;
  }

  const [subscriptionInfo, userCredits] = await Promise.all([
    getUserSubscriptionInfo(userUuid),
    getUserCredits(userUuid)
  ]);

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">{t("subscription.title")}</h1>
        <p className="text-muted-foreground">
          {t("subscription.description")}
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <SubscriptionDashboard 
          subscriptionInfo={subscriptionInfo}
          userCredits={userCredits}
        />
      </Suspense>
    </div>
  );
}
