import { NextRequest, NextResponse } from 'next/server';
import { doSendFeishuMessage, formatContactMessageForFeishu } from '@/services/feishu';
import AnalyticsUtils from '@/lib/analytics';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, email, app, purpose, location, message } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Name, email, and message are required fields' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Please enter a valid email address' },
        { status: 400 }
      );
    }

    // Format message content
    const formattedMessage = formatContactMessageForFeishu({
      name,
      email,
      app,
      purpose,
      location,
      message
    });

    // Send to Feishu
    await doSendFeishuMessage(formattedMessage);

    // Record analytics event
    try {
      // Track contact form submission event on server side
      if (typeof window !== 'undefined') {
        AnalyticsUtils.trackConversion('contact_form_submit', undefined, {
          app_selection: app,
          purpose: purpose,
          user_location: location
        });
      }
    } catch (analyticsError) {
      // Analytics failure should not affect main functionality
      console.warn('Analytics tracking failed:', analyticsError);
    }

    return NextResponse.json(
      { 
        success: true, 
        message: 'Message sent successfully! We will get back to you soon.' 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form submission error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to send message. Please try again later. If the problem persists, please contact us directly.' 
      },
      { status: 500 }
    );
  }
} 