import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";
import { createSubscription, updateSubscription, cancelSubscription } from "@/models/subscription";
import { updateUserCredits } from "@/services/credit";

// Create Stripe instance function to be called at runtime
function getStripeInstance() {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  if (!stripeSecretKey) {
    throw new Error("STRIPE_SECRET_KEY is not set in environment variables");
  }
  
  return new Stripe(stripeSecretKey, {
    apiVersion: "2024-12-18.acacia",
  });
}

export async function POST(request: NextRequest) {
  const stripe = getStripeInstance();
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;
  const body = await request.text();
  const headersList = headers();
  const sig = headersList.get("stripe-signature");

  if (!sig) {
    console.error("No Stripe signature found");
    return NextResponse.json({ error: "No signature" }, { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
  } catch (err) {
    console.error("Webhook signature verification failed:", err);
    return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
  }

  console.log(`Received Stripe webhook: ${event.type}`);

  try {
    switch (event.type) {
      case "checkout.session.completed":
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
        break;

      case "customer.subscription.created":
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription);
        break;

      case "customer.subscription.updated":
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription);
        break;

      case "customer.subscription.deleted":
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription);
        break;

      case "invoice.payment_succeeded":
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case "invoice.payment_failed":
        await handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook handler error:", error);
    return NextResponse.json(
      { error: "Webhook handler failed" },
      { status: 500 }
    );
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  console.log("Checkout session completed:", session.id);
  
  if (session.mode === "subscription" && session.subscription) {
    // Subscription checkout completed
    const stripe = getStripeInstance();
    const subscription = await stripe.subscriptions.retrieve(
      session.subscription as string,
      { expand: ["items.data.price"] }
    );
    
    await handleSubscriptionCreated(subscription);
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log("Subscription created:", subscription.id);
  
  const stripe = getStripeInstance();
  const customerId = subscription.customer as string;
  const customer = await stripe.customers.retrieve(customerId);
  
  if (customer.deleted) {
    console.error("Customer was deleted");
    return;
  }
  
  const userEmail = customer.email;
  if (!userEmail) {
    console.error("No email found for customer");
    return;
  }

  // Get user by email to find user_id
  const { findUserByEmail } = await import("@/models/user");
  const user = await findUserByEmail(userEmail);
  
  if (!user) {
    console.error("User not found for email:", userEmail);
    return;
  }

  // Extract plan information from subscription
  const priceId = subscription.items.data[0]?.price.id;
  let planId = "free";
  
  if (priceId?.includes("pro")) {
    planId = "pro";
  } else if (priceId?.includes("explorer")) {
    planId = "explorer";
  }

  // Get plan details
  const { SUBSCRIPTION_PLANS } = await import("@/models/subscription");
  const plan = SUBSCRIPTION_PLANS[planId];
  
  // Determine billing interval from price ID
  const billingInterval = priceId?.includes("yearly") ? "year" : "month";
  const pricing = billingInterval === "year" ? plan.pricing.yearly : plan.pricing.monthly;

  // Create subscription record
  await createSubscription({
    user_id: user.id,
    stripe_subscription_id: subscription.id,
    stripe_customer_id: customerId,
    stripe_price_id: priceId,
    plan_id: planId as "pro" | "free" | "explorer",
    plan_name: plan.name,
    credits_included: plan.credits.monthly || 0,
    price_amount: pricing?.amount || 0,
    price_currency: pricing?.currency || "USD",
    billing_interval: billingInterval as "month" | "year",
    status: subscription.status as "active" | "canceled" | "past_due" | "unpaid",
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    cancel_at_period_end: subscription.cancel_at_period_end,
  });

  // Allocate monthly credits based on plan
  if (plan.credits.monthly) {
    await updateUserCredits(user.id, plan.credits.monthly, "subscription_allocation");
  }

  console.log(`Subscription created for user ${user.id} with plan ${planId}`);
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log("Subscription updated:", subscription.id);
  
  // Update subscription status and period
  await updateSubscription(subscription.id, {
    status: subscription.status as "active" | "canceled" | "past_due" | "unpaid",
    current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
    current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    cancel_at_period_end: subscription.cancel_at_period_end,
  });
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log("Subscription deleted:", subscription.id);
  
  // Mark subscription as canceled
  await updateSubscription(subscription.id, {
    status: "canceled",
    canceled_at: new Date().toISOString(),
  });
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log("Invoice payment succeeded:", invoice.id);
  
  if (invoice.subscription) {
    const stripe = getStripeInstance();
    const subscription = await stripe.subscriptions.retrieve(
      invoice.subscription as string
    );
    
    // Allocate monthly credits for successful payment
    const customerId = subscription.customer as string;
    const customer = await stripe.customers.retrieve(customerId);
    
    if (!customer.deleted && customer.email) {
      const { findUserByEmail } = await import("@/models/user");
      const user = await findUserByEmail(customer.email);
      
      if (user) {
        // Get plan from subscription
        const priceId = subscription.items.data[0]?.price.id;
        let planId = "free";
        
        if (priceId?.includes("pro")) {
          planId = "pro";
        } else if (priceId?.includes("explorer")) {
          planId = "explorer";
        }

        const { SUBSCRIPTION_PLANS } = await import("@/models/subscription");
        const plan = SUBSCRIPTION_PLANS[planId];
        
        if (plan.credits.monthly) {
          await updateUserCredits(user.id, plan.credits.monthly, "monthly_allocation");
        }
      }
    }
  }
}

async function handleInvoicePaymentFailed(invoice: Stripe.Invoice) {
  console.log("Invoice payment failed:", invoice.id);
  
  // Could implement logic to handle failed payments
  // e.g., send notification emails, update subscription status
}
