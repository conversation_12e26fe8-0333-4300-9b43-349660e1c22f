import { NextRequest, NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";

// This endpoint should be called by a cron job service (like Vercel Cron or external service)
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from authorized source
    const authHeader = request.headers.get("authorization");
    const expectedToken = process.env.CRON_SECRET_TOKEN;
    
    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const resetType = searchParams.get("type"); // "daily" or "monthly"

    if (resetType === "daily") {
      await handleDailyReset();
    } else if (resetType === "monthly") {
      await handleMonthlyReset();
    } else {
      return NextResponse.json(
        { error: "Invalid reset type. Use 'daily' or 'monthly'" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `${resetType} credit reset completed`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Credit reset failed:", error);
    return NextResponse.json(
      { error: "Credit reset failed" },
      { status: 500 }
    );
  }
}

async function handleDailyReset() {
  const supabase = getSupabaseClient();
  
  console.log("Starting daily credit reset...");
  
  // Reset daily usage for free users
  // This is handled by the database function, but we can also track it here
  
  // Get all free users (users without active subscriptions)
  const { data: freeUsers, error: freeUsersError } = await supabase
    .from("users")
    .select(`
      uuid,
      email,
      subscriptions!left (
        id,
        status
      )
    `)
    .or("subscriptions.status.is.null,subscriptions.status.neq.active", { foreignTable: "subscriptions" });

  if (freeUsersError) {
    throw new Error(`Failed to fetch free users: ${freeUsersError.message}`);
  }

  // Filter users who don't have active subscriptions
  const actualFreeUsers = freeUsers?.filter(user => 
    !user.subscriptions || user.subscriptions.length === 0 || 
    !user.subscriptions.some((sub: any) => sub.status === 'active')
  ) || [];

  console.log(`Found ${actualFreeUsers.length} free users for daily reset`);

  // Log daily reset for tracking
  for (const user of actualFreeUsers) {
    await supabase
      .from("credits")
      .insert({
        user_id: user.uuid,
        trans_type: "daily_reset",
        credits: 5, // Free plan daily credits
        balance: 5,
        trans_no: `daily_reset_${new Date().toISOString().split('T')[0]}_${user.uuid}`,
        created_at: new Date().toISOString(),
      });
  }

  console.log("Daily credit reset completed");
}

async function handleMonthlyReset() {
  const supabase = getSupabaseClient();
  
  console.log("Starting monthly credit reset...");
  
  // Get all active subscriptions
  const { data: activeSubscriptions, error: subscriptionsError } = await supabase
    .from("subscriptions")
    .select(`
      id,
      user_id,
      plan_id,
      status,
      current_period_start,
      current_period_end
    `)
    .eq("status", "active");

  if (subscriptionsError) {
    throw new Error(`Failed to fetch active subscriptions: ${subscriptionsError.message}`);
  }

  console.log(`Found ${activeSubscriptions?.length || 0} active subscriptions`);

  if (!activeSubscriptions || activeSubscriptions.length === 0) {
    console.log("No active subscriptions found");
    return;
  }

  // Import subscription plans
  const { SUBSCRIPTION_PLANS } = await import("@/models/subscription");

  // Allocate monthly credits for each active subscription
  for (const subscription of activeSubscriptions) {
    const plan = SUBSCRIPTION_PLANS[subscription.plan_id as keyof typeof SUBSCRIPTION_PLANS];
    
    if (plan && plan.credits.monthly) {
      // Add monthly credits
      await supabase
        .from("credits")
        .insert({
          user_id: subscription.user_id,
          trans_type: "monthly_allocation",
          credits: plan.credits.monthly,
          balance: plan.credits.monthly, // This should be calculated based on current balance
          trans_no: `monthly_${new Date().toISOString().split('T')[0]}_${subscription.user_id}`,
          created_at: new Date().toISOString(),
        });

      console.log(`Allocated ${plan.credits.monthly} credits to user ${subscription.user_id} (${plan.name})`);
    }
  }

  console.log("Monthly credit reset completed");
}

// GET endpoint for health check
export async function GET() {
  return NextResponse.json({
    status: "ok",
    service: "credit-reset",
    timestamp: new Date().toISOString(),
  });
}
