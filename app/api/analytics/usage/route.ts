import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { getSupabaseClient } from "@/models/db";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get("period") || "30d"; // 7d, 30d, 90d, 1y
    const userId = session.user.uuid;

    const supabase = getSupabaseClient();

    // Calculate date range based on period
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get credit transactions for the period
    const { data: transactions, error: transactionsError } = await supabase
      .from("credits")
      .select("*")
      .eq("user_id", userId)
      .gte("created_at", startDate.toISOString())
      .lte("created_at", endDate.toISOString())
      .order("created_at", { ascending: true });

    if (transactionsError) {
      throw new Error(`Failed to fetch transactions: ${transactionsError.message}`);
    }

    // Get task history for the period
    const { data: tasks, error: tasksError } = await supabase
      .from("tasks")
      .select("*")
      .eq("user_id", userId)
      .gte("created_at", startDate.toISOString())
      .lte("created_at", endDate.toISOString())
      .order("created_at", { ascending: true });

    if (tasksError) {
      throw new Error(`Failed to fetch tasks: ${tasksError.message}`);
    }

    // Calculate usage statistics
    const stats = calculateUsageStats(transactions || [], tasks || [], period);

    return NextResponse.json({
      success: true,
      data: {
        period,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        stats,
        transactions: transactions || [],
        tasks: tasks || [],
      },
    });

  } catch (error) {
    console.error("Usage analytics failed:", error);
    return NextResponse.json(
      { error: "Failed to fetch usage analytics" },
      { status: 500 }
    );
  }
}

function calculateUsageStats(transactions: any[], tasks: any[], period: string) {
  // Calculate total credits used
  const creditsUsed = transactions
    .filter(t => t.credits < 0) // Negative credits are usage
    .reduce((sum, t) => sum + Math.abs(t.credits), 0);

  // Calculate total credits received
  const creditsReceived = transactions
    .filter(t => t.credits > 0) // Positive credits are allocations
    .reduce((sum, t) => sum + t.credits, 0);

  // Calculate task statistics
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === 'completed').length;
  const failedTasks = tasks.filter(t => t.status === 'failed').length;
  const processingTasks = tasks.filter(t => t.status === 'processing').length;

  // Calculate success rate
  const successRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Group by transaction type
  const transactionsByType = transactions.reduce((acc, t) => {
    acc[t.trans_type] = (acc[t.trans_type] || 0) + Math.abs(t.credits);
    return acc;
  }, {});

  // Group by task type
  const tasksByType = tasks.reduce((acc, t) => {
    acc[t.type] = (acc[t.type] || 0) + 1;
    return acc;
  }, {});

  // Daily usage breakdown (for charts)
  const dailyUsage = getDailyUsageBreakdown(transactions, period);

  return {
    credits: {
      used: creditsUsed,
      received: creditsReceived,
      net: creditsReceived - creditsUsed,
    },
    tasks: {
      total: totalTasks,
      completed: completedTasks,
      failed: failedTasks,
      processing: processingTasks,
      success_rate: Math.round(successRate * 100) / 100,
    },
    breakdown: {
      by_transaction_type: transactionsByType,
      by_task_type: tasksByType,
    },
    daily_usage: dailyUsage,
  };
}

function getDailyUsageBreakdown(transactions: any[], period: string) {
  const days = period === "7d" ? 7 : period === "30d" ? 30 : period === "90d" ? 90 : 365;
  const dailyUsage: { date: string; credits_used: number; credits_received: number }[] = [];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];

    const dayTransactions = transactions.filter(t => 
      t.created_at.startsWith(dateStr)
    );

    const creditsUsed = dayTransactions
      .filter(t => t.credits < 0)
      .reduce((sum, t) => sum + Math.abs(t.credits), 0);

    const creditsReceived = dayTransactions
      .filter(t => t.credits > 0)
      .reduce((sum, t) => sum + t.credits, 0);

    dailyUsage.push({
      date: dateStr,
      credits_used: creditsUsed,
      credits_received: creditsReceived,
    });
  }

  return dailyUsage;
}
