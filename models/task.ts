import { Task, CreateTaskRequest } from "@/types/task";
import { getSupabaseClient } from "./db";

export async function createTask(task: CreateTaskRequest & { user_id: string }): Promise<Task> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("tasks")
    .insert({
      user_id: task.user_id,
      task_type: task.task_type,
      input_urls: task.input_urls,
      scale: task.scale,
      credits_cost: task.credits_cost,
      total_images: task.input_urls.length,
      metadata: task.metadata || {}
    })
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function getTaskById(id: string): Promise<Task | null> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("tasks")
    .select("*")
    .eq("id", id)
    .single();

  if (error) {
    return null;
  }

  return data;
}

export async function getTasksByUser(
  user_id: string,
  page: number = 1,
  limit: number = 50
): Promise<Task[]> {
  if (page < 1) page = 1;
  if (limit <= 0) limit = 50;

  const offset = (page - 1) * limit;
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("tasks")
    .select("*")
    .eq("user_id", user_id)
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}

export async function updateTaskStatus(
  id: string,
  status: Task['status'],
  updates?: Partial<Pick<Task, 'processed_images' | 'output_urls' | 'error_message' | 'completed_at' | 'processing_time_seconds'>>
): Promise<Task> {
  const supabase = getSupabaseClient();
  
  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };

  if (updates) {
    Object.assign(updateData, updates);
  }

  if (status === 'completed' && !updates?.completed_at) {
    updateData.completed_at = new Date().toISOString();
  }

  const { data, error } = await supabase
    .from("tasks")
    .update(updateData)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function addExternalTaskId(taskId: string, externalTaskId: string): Promise<void> {
  const supabase = getSupabaseClient();
  
  // First get current external_task_ids
  const { data: currentTask } = await supabase
    .from("tasks")
    .select("external_task_ids")
    .eq("id", taskId)
    .single();

  if (!currentTask) {
    throw new Error("Task not found");
  }

  const currentIds = currentTask.external_task_ids || [];
  const updatedIds = [...currentIds, externalTaskId];

  const { error } = await supabase
    .from("tasks")
    .update({ 
      external_task_ids: updatedIds,
      updated_at: new Date().toISOString()
    })
    .eq("id", taskId);

  if (error) {
    throw error;
  }
}

export async function getActiveTasksByUser(user_id: string): Promise<Task[]> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from("tasks")
    .select("*")
    .eq("user_id", user_id)
    .in("status", ["pending", "processing"])
    .order("created_at", { ascending: false });

  if (error) {
    throw error;
  }

  return data || [];
}