import { createClient } from "@supabase/supabase-js";

export function getSupabaseClient() {
  const supabaseUrl = process.env.SUPABASE_URL || "";

  let supabaseKey = process.env.SUPABASE_ANON_KEY || "";
  if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
    supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  }

  if (!supabaseUrl || !supabaseKey) {
    console.error("❌ [Supabase] Configuration missing:", { 
      hasUrl: !!supabaseUrl, 
      hasKey: !!supabaseKey,
      keyType: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'service_role' : 'anon'
    });
    throw new Error("Supabase URL or key is not set");
  }

  console.log("✅ [Supabase] Client configured with:", { 
    url: supabaseUrl, 
    keyType: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'service_role' : 'anon'
  });

  const client = createClient(supabaseUrl, supabaseKey);

  return client;
}
