import { Subscription, SubscriptionPlan, UserSubscriptionInfo } from "@/types/subscription";
import { getSupabaseClient } from "./db";

export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlan> = {
  free: {
    id: 'free',
    name: 'Free Plan',
    description: 'Perfect for trying out our service',
    features: [
      '5 daily credits (resets at UTC midnight)',
      'Single image processing only',
      'Standard quality upscaling',
      'Community support'
    ],
    pricing: {},
    credits: {
      daily: 5
    },
    limits: {
      batch_processing: false,
      priority_queue: false
    }
  },
  pro: {
    id: 'pro',
    name: 'Pro Plan',
    description: 'Perfect for content creators and professionals',
    features: [
      '500 monthly credits',
      'Batch processing support',
      'Priority processing queue',
      'Email support',
      'No daily limits'
    ],
    pricing: {
      monthly: {
        amount: 999, // $9.99 in cents
        currency: 'USD',
        stripe_price_id: 'price_pro_monthly_v2'
      },
      yearly: {
        amount: 9999, // $99.99 in cents
        currency: 'USD',
        stripe_price_id: 'price_pro_yearly_v2',
        discount_percentage: 17
      }
    },
    credits: {
      monthly: 500
    },
    limits: {
      batch_processing: true,
      max_batch_size: 50,
      priority_queue: true
    }
  },
  explorer: {
    id: 'explorer',
    name: 'Explorer Plan',
    description: 'For heavy users and small teams',
    features: [
      '2000 monthly credits',
      'Batch processing support',
      'Fastest processing queue',
      'Priority support',
      'Advanced analytics',
      'No daily limits'
    ],
    pricing: {
      monthly: {
        amount: 1999, // $19.99 in cents
        currency: 'USD',
        stripe_price_id: 'price_explorer_monthly_v2'
      },
      yearly: {
        amount: 19990, // $199.90 in cents
        currency: 'USD',
        stripe_price_id: 'price_explorer_yearly_v2',
        discount_percentage: 17
      }
    },
    credits: {
      monthly: 2000
    },
    limits: {
      batch_processing: true,
      max_batch_size: 100,
      priority_queue: true
    }
  }
};

export async function getUserSubscription(user_id: string): Promise<Subscription | null> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("subscriptions")
    .select("*")
    .eq("user_id", user_id)
    .eq("status", "active")
    .order("created_at", { ascending: false })
    .limit(1)
    .single();

  if (error || !data) {
    return null;
  }

  return data;
}

export async function createSubscription(subscription: Omit<Subscription, 'id' | 'created_at' | 'updated_at'>): Promise<Subscription> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("subscriptions")
    .insert(subscription)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function updateSubscription(
  id: string,
  updates: Partial<Subscription>
): Promise<Subscription> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("subscriptions")
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export async function cancelSubscription(id: string): Promise<Subscription> {
  const supabase = getSupabaseClient();
  
  const { data, error } = await supabase
    .from("subscriptions")
    .update({
      cancel_at_period_end: true,
      updated_at: new Date().toISOString()
    })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

export function getSubscriptionPlan(planId: string): SubscriptionPlan {
  return SUBSCRIPTION_PLANS[planId] || SUBSCRIPTION_PLANS.free;
}

export function calculateYearlySavings(planId: 'pro' | 'explorer'): number {
  const plan = SUBSCRIPTION_PLANS[planId];
  if (!plan.pricing.monthly || !plan.pricing.yearly) return 0;

  const monthlyYearly = plan.pricing.monthly.amount * 12;
  const yearly = plan.pricing.yearly.amount;
  return monthlyYearly - yearly;
}

export async function getUserSubscriptionInfo(user_id: string): Promise<UserSubscriptionInfo> {
  const supabase = getSupabaseClient();

  // Get user's active subscription
  const subscription = await getUserSubscription(user_id);

  // Determine the plan
  let planId = 'free';
  if (subscription) {
    planId = subscription.plan_id;
  }

  const plan = getSubscriptionPlan(planId);

  // Get user's current credits balance from the users table
  const { data: userData } = await supabase
    .from("users")
    .select("credits_balance, daily_usage_count, daily_usage_reset_at")
    .eq("id", user_id)
    .single();

  const credits_balance = userData?.credits_balance || (plan.id === 'free' ? 5 : 0);

  // Get daily usage for free users
  let daily_usage_count = userData?.daily_usage_count || 0;
  let daily_usage_reset_at = userData?.daily_usage_reset_at || new Date().toISOString();

  if (plan.id === 'free') {
    const resetDate = new Date(daily_usage_reset_at);
    const now = new Date();
    
    // Check if we need to reset daily usage (new UTC day)
    if (resetDate.getUTCDate() !== now.getUTCDate() || 
        resetDate.getUTCMonth() !== now.getUTCMonth() || 
        resetDate.getUTCFullYear() !== now.getUTCFullYear()) {
      
      // Reset daily usage
      daily_usage_count = 0;
      daily_usage_reset_at = new Date();
      daily_usage_reset_at.setUTCHours(24, 0, 0, 0); // Next UTC midnight
      
      // Update the user's daily usage reset time
      await supabase
        .from("users")
        .update({ 
          daily_usage_count: 0,
          daily_usage_reset_at: daily_usage_reset_at.toISOString(),
          credits_balance: 5 // Reset to 5 daily credits
        })
        .eq("id", user_id);
    }
  }

  return {
    subscription: subscription || undefined,
    plan,
    credits_balance,
    daily_usage_count,
    daily_usage_reset_at: daily_usage_reset_at,
    can_use_batch: plan.limits.batch_processing
  };
}