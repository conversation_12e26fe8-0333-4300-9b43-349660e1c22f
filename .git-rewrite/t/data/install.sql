-- =============================================================================
-- Image Upscaler SaaS Database Initialization
-- Modern unified schema with authentication, subscriptions, and task management
-- =============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================================================
-- 1. Create modern users table
-- =============================================================================
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    avatar_url VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Authentication details
    provider VARCHAR(50) NOT NULL DEFAULT 'google',
    provider_id VARCHAR(255) NOT NULL,
    
    -- Subscription details
    subscription_status VARCHAR(50) DEFAULT 'free', -- free, pro, explorer
    subscription_tier VARCHAR(50) DEFAULT 'free',
    credits_balance INTEGER DEFAULT 5, -- Free users start with 5 daily credits
    
    -- Usage tracking
    daily_usage_count INTEGER DEFAULT 0,
    daily_usage_reset_at TIMESTAMPTZ DEFAULT NOW(),
    total_usage_count INTEGER DEFAULT 0,
    
    -- User preferences
    locale VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(100) DEFAULT 'UTC',
    last_login_at TIMESTAMPTZ,
    last_login_ip VARCHAR(45),
    
    -- Legacy compatibility fields
    uuid VARCHAR(255) UNIQUE, -- Keep for migration compatibility
    nickname VARCHAR(255),
    signin_type VARCHAR(50),
    signin_provider VARCHAR(50),
    signin_openid VARCHAR(255),
    signin_ip VARCHAR(255),
    
    -- Status
    status VARCHAR(20) DEFAULT 'active',
    
    UNIQUE(provider, provider_id)
);

-- =============================================================================
-- 2. Create subscriptions table
-- =============================================================================
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Stripe integration
    stripe_subscription_id VARCHAR(255) UNIQUE,
    stripe_customer_id VARCHAR(255),
    stripe_price_id VARCHAR(255),
    
    -- Plan details
    plan_id VARCHAR(50) NOT NULL, -- free, pro, explorer
    plan_name VARCHAR(100) NOT NULL,
    credits_included INTEGER NOT NULL DEFAULT 0,
    price_amount INTEGER NOT NULL, -- in cents
    price_currency VARCHAR(3) DEFAULT 'USD',
    billing_interval VARCHAR(20) NOT NULL, -- month, year
    
    -- Subscription status
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- 3. Create tasks table
-- =============================================================================
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Task details
    task_type VARCHAR(50) NOT NULL, -- 'single', 'batch'
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
    
    -- Task configuration
    scale INTEGER NOT NULL DEFAULT 2,
    total_images INTEGER DEFAULT 1,
    processed_images INTEGER DEFAULT 0,
    
    -- External task tracking (for integration with app-iu.a1d.ai)
    external_task_ids JSONB DEFAULT '[]'::jsonb,
    
    -- Results
    input_urls JSONB NOT NULL, -- Array of input image URLs
    output_urls JSONB DEFAULT '[]'::jsonb, -- Array of processed image URLs
    
    -- Metadata
    credits_cost INTEGER NOT NULL,
    processing_time_seconds INTEGER,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- =============================================================================
-- 4. Create credits table
-- =============================================================================
CREATE TABLE credits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Transaction details
    transaction_type VARCHAR(50) NOT NULL, -- purchase, usage, bonus, refund, expiry, daily_reset
    credits_amount INTEGER NOT NULL, -- positive for added, negative for used
    credits_balance_after INTEGER NOT NULL,
    
    -- Reference information
    task_id UUID REFERENCES tasks(id),
    subscription_id UUID REFERENCES subscriptions(id),
    stripe_payment_intent_id VARCHAR(255),
    
    -- Legacy compatibility
    trans_no VARCHAR(255) UNIQUE, -- Keep for migration
    order_no VARCHAR(255),
    expired_at TIMESTAMPTZ,
    
    -- Metadata
    description TEXT,
    metadata JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- 5. Create payments table (replaces orders)
-- =============================================================================
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES subscriptions(id),
    
    -- Stripe details
    stripe_payment_intent_id VARCHAR(255) UNIQUE,
    stripe_charge_id VARCHAR(255),
    stripe_invoice_id VARCHAR(255),
    
    -- Payment details
    amount INTEGER NOT NULL, -- in cents
    currency VARCHAR(3) DEFAULT 'USD',
    credits_purchased INTEGER DEFAULT 0,
    
    -- Status
    status VARCHAR(50) NOT NULL,
    failure_reason TEXT,
    
    -- Legacy compatibility fields
    order_no VARCHAR(255) UNIQUE,
    interval VARCHAR(50),
    expired_at TIMESTAMPTZ,
    stripe_session_id VARCHAR(255),
    sub_id VARCHAR(255),
    sub_interval_count INTEGER,
    sub_cycle_anchor INTEGER,
    sub_period_end INTEGER,
    sub_period_start INTEGER,
    sub_times INTEGER,
    product_id VARCHAR(255),
    product_name VARCHAR(255),
    valid_months INTEGER,
    order_detail TEXT,
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT,
    
    description TEXT,
    metadata JSONB,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- 6. Create api_keys table
-- =============================================================================
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Key details
    key_hash VARCHAR(255) UNIQUE NOT NULL,
    key_prefix VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    
    -- Legacy compatibility
    api_key VARCHAR(255) UNIQUE, -- Keep original key for migration
    title VARCHAR(100),
    
    -- Permissions and limits
    permissions JSONB DEFAULT '["image-upscale"]'::jsonb,
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    
    -- Status
    status VARCHAR(20) DEFAULT 'active',
    last_used_at TIMESTAMPTZ,
    last_used_ip VARCHAR(45),
    usage_count INTEGER DEFAULT 0,
    
    expires_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================================================
-- 7. Create posts table (for blog/content)
-- =============================================================================
CREATE TABLE posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'draft',
    cover_url VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50) DEFAULT 'en'
);

-- =============================================================================
-- 8. Create indexes for performance
-- =============================================================================

-- Users indexes
CREATE INDEX idx_users_email_provider ON users(email, provider);
CREATE INDEX idx_users_subscription_status ON users(subscription_status);
CREATE INDEX idx_users_daily_usage ON users(daily_usage_reset_at, daily_usage_count);
CREATE INDEX idx_users_uuid ON users(uuid);

-- Subscriptions indexes
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_user_status ON subscriptions(user_id, status);

-- Tasks indexes
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_tasks_user_status ON tasks(user_id, status);

-- Credits indexes
CREATE INDEX idx_credits_user_id ON credits(user_id);
CREATE INDEX idx_credits_created_at ON credits(created_at);
CREATE INDEX idx_credits_transaction_type ON credits(transaction_type);
CREATE INDEX idx_credits_trans_no ON credits(trans_no);

-- Payments indexes
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_stripe_payment_intent_id ON payments(stripe_payment_intent_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_order_no ON payments(order_no);

-- API keys indexes
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_status ON api_keys(status);
CREATE INDEX idx_api_keys_api_key ON api_keys(api_key);

-- =============================================================================
-- 9. Create functions for credit management
-- =============================================================================

-- Function to update user's credits balance
CREATE OR REPLACE FUNCTION update_user_credits(
    p_user_id UUID,
    p_credits_change INTEGER,
    p_transaction_type VARCHAR(50),
    p_description TEXT DEFAULT NULL,
    p_task_id UUID DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
    v_new_balance INTEGER;
BEGIN
    -- Update user's credits balance
    UPDATE users 
    SET credits_balance = credits_balance + p_credits_change,
        updated_at = NOW()
    WHERE id = p_user_id
    RETURNING credits_balance INTO v_new_balance;
    
    -- Log the transaction
    INSERT INTO credits (
        user_id, 
        transaction_type, 
        credits_amount, 
        credits_balance_after, 
        description,
        task_id
    ) VALUES (
        p_user_id, 
        p_transaction_type, 
        p_credits_change, 
        v_new_balance, 
        p_description,
        p_task_id
    );
    
    RETURN v_new_balance;
END;
$$ LANGUAGE plpgsql;

-- Function to reset daily credits for free users
CREATE OR REPLACE FUNCTION reset_daily_credits() RETURNS INTEGER AS $$
DECLARE
    v_affected_users INTEGER := 0;
BEGIN
    -- Reset daily credits for free users (UTC timezone)
    UPDATE users 
    SET 
        credits_balance = 5,
        daily_usage_count = 0,
        daily_usage_reset_at = NOW(),
        updated_at = NOW()
    WHERE 
        subscription_tier = 'free' 
        AND daily_usage_reset_at::date < NOW()::date;
    
    GET DIAGNOSTICS v_affected_users = ROW_COUNT;
    
    -- Log the reset transactions
    INSERT INTO credits (user_id, transaction_type, credits_amount, credits_balance_after, description)
    SELECT 
        id,
        'daily_reset',
        5,
        5,
        'Daily credits reset for free tier'
    FROM users 
    WHERE 
        subscription_tier = 'free' 
        AND daily_usage_reset_at::date = NOW()::date;
    
    RETURN v_affected_users;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can perform operation
CREATE OR REPLACE FUNCTION can_use_credits(
    p_user_id UUID,
    p_credits_needed INTEGER,
    p_operation_type VARCHAR(50) DEFAULT 'image_upscale'
) RETURNS BOOLEAN AS $$
DECLARE
    v_user_record RECORD;
BEGIN
    SELECT * INTO v_user_record FROM users WHERE id = p_user_id;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Reset daily usage if it's a new day (for free users)
    IF v_user_record.subscription_tier = 'free' 
       AND v_user_record.daily_usage_reset_at::date < NOW()::date THEN
        PERFORM reset_daily_credits();
        -- Refresh user record
        SELECT * INTO v_user_record FROM users WHERE id = p_user_id;
    END IF;
    
    -- Check if user has enough credits
    IF v_user_record.credits_balance >= p_credits_needed THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- 10. Create triggers for automatic updates
-- =============================================================================

-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- 11. Insert sample data (optional)
-- =============================================================================

-- Insert sample posts
INSERT INTO posts (uuid, slug, title, description, content, status, locale) VALUES
('sample-post-1', 'getting-started', 'Getting Started with Image Upscaling', 'Learn how to use our image upscaling service', 'Welcome to our image upscaling platform...', 'published', 'en'),
('sample-post-2', 'api-documentation', 'API Documentation', 'Complete guide to our API', 'Our API allows you to integrate image upscaling...', 'published', 'en');

-- =============================================================================
-- Database initialization complete!
-- =============================================================================