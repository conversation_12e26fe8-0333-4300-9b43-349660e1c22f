import { track } from '@vercel/analytics';

/**
 * Analytics utility functions for tracking user events
 */
export class AnalyticsUtils {
  /**
   * Track a custom event
   * @param name - Event name
   * @param properties - Event properties (optional)
   */
  static trackEvent(name: string, properties?: Record<string, any>) {
    if (typeof window !== 'undefined') {
      track(name, properties);
    }
  }

  /**
   * Track page view (automatically handled by Analytics component, but can be used for SPA navigation)
   * @param page - Page name or path
   * @param properties - Additional properties
   */
  static trackPageView(page: string, properties?: Record<string, any>) {
    this.trackEvent('page_view', { page, ...properties });
  }

  /**
   * Track user interaction
   * @param element - Element type (button, link, etc.)
   * @param action - Action performed
   * @param properties - Additional properties
   */
  static trackInteraction(element: string, action: string, properties?: Record<string, any>) {
    this.trackEvent('user_interaction', { element, action, ...properties });
  }

  /**
   * Track conversion events
   * @param type - Conversion type
   * @param value - Conversion value (optional)
   * @param properties - Additional properties
   */
  static trackConversion(type: string, value?: number, properties?: Record<string, any>) {
    this.trackEvent('conversion', { type, value, ...properties });
  }
}

export default AnalyticsUtils; 