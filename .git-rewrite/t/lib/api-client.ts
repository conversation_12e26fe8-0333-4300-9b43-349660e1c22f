/**
 * API Client for frontend-backend communication
 * Handles authentication, error handling, and request/response formatting
 */

import { createProxyFetch } from "@/lib/proxy";

interface ApiClientConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ApiClient {
  private baseUrl: string;
  private apiKey?: string;
  private timeout: number;
  private fetch: typeof fetch;

  constructor(config: ApiClientConfig) {
    this.baseUrl = config.baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.apiKey = config.apiKey;
    this.timeout = config.timeout || 30000; // 30 seconds default
    this.fetch = createProxyFetch(); // 使用支持代理的 fetch
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await this.fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText || response.statusText}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            error: 'Request timeout',
          };
        }
        return {
          success: false,
          error: error.message,
        };
      }
      
      return {
        success: false,
        error: 'Unknown error occurred',
      };
    }
  }

  // Task Management APIs
  async createTask(taskData: {
    input_urls: string[];
    scale: number;
    user_id: string;
    type: 'single_upscale' | 'batch_upscale';
  }) {
    return this.request('/task', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  async getTask(taskId: string) {
    return this.request(`/task/${taskId}`);
  }

  async getTaskStatus(taskId: string) {
    return this.request(`/task/${taskId}/status`);
  }

  async cancelTask(taskId: string) {
    return this.request(`/task/${taskId}/cancel`, {
      method: 'POST',
    });
  }

  // User Management APIs
  async getUserInfo(userId: string) {
    return this.request(`/user/${userId}`);
  }

  async updateUserCredits(userId: string, credits: number, transactionType: string) {
    return this.request(`/user/${userId}/credits`, {
      method: 'POST',
      body: JSON.stringify({
        credits,
        transaction_type: transactionType,
      }),
    });
  }

  // Subscription Management APIs
  async createCheckoutSession(data: {
    price_id: string;
    user_id: string;
    success_url: string;
    cancel_url: string;
  }) {
    return this.request('/subscription/checkout', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async cancelSubscription(subscriptionId: string) {
    return this.request(`/subscription/${subscriptionId}/cancel`, {
      method: 'POST',
    });
  }

  async getSubscriptionInfo(userId: string) {
    return this.request(`/subscription/user/${userId}`);
  }

  // Health Check
  async healthCheck() {
    return this.request('/health');
  }
}

// Validate required environment variables
if (!process.env.NEXT_PUBLIC_BACKEND_API_URL) {
  throw new Error('NEXT_PUBLIC_BACKEND_API_URL environment variable is required');
}

// Create singleton instance
const apiClient = new ApiClient({
  baseUrl: process.env.NEXT_PUBLIC_BACKEND_API_URL,
  apiKey: process.env.BACKEND_API_KEY,
  timeout: 30000,
});

export { apiClient, ApiClient };
export type { ApiResponse, ApiClientConfig };
