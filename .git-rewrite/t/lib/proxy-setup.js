/**
 * 全局代理配置
 * 借鉴 toylivewall 的代理实现方式
 * 只在开发环境且明确启用时才配置代理
 */

function setupProxy() {
  // 只在明确启用代理时才配置
  if (process.env.ENABLE_PROXY !== 'true') {
    console.log('📡 代理未启用 (ENABLE_PROXY != true)');
    return;
  }

  // 只在开发环境配置代理
  if (process.env.NODE_ENV === 'production') {
    console.log('🚫 生产环境不启用代理');
    return;
  }

  const proxyUrl = process.env.PROXY_URL || 'http://127.0.0.1:7890';
  
  try {
    console.log('🔧 [Proxy Setup] 开始配置全局代理...');
    
    // 方法 1: 配置环境变量（兼容性最好）
    process.env.HTTP_PROXY = proxyUrl;
    process.env.HTTPS_PROXY = proxyUrl;
    process.env.ALL_PROXY = proxyUrl;
    console.log('✅ [Proxy Setup] 环境变量代理已配置');

    // 方法 2: 尝试配置 undici 代理（用于现代 fetch）
    try {
      const { ProxyAgent, setGlobalDispatcher } = require('undici');
      const proxyAgent = new ProxyAgent(proxyUrl);
      setGlobalDispatcher(proxyAgent);
      console.log('✅ [Proxy Setup] undici 全局代理已配置');
    } catch (error) {
      console.log('⚠️  [Proxy Setup] undici 代理配置失败，尝试其他方法');
    }

    // 方法 3: 尝试配置传统 HTTP 代理
    try {
      const https = require('https');
      const http = require('http');
      
      // 尝试使用 https-proxy-agent
      try {
        const { HttpsProxyAgent } = require('https-proxy-agent');
        const { HttpProxyAgent } = require('http-proxy-agent');
        
        // 设置全局 agent
        const httpsAgent = new HttpsProxyAgent(proxyUrl);
        const httpAgent = new HttpProxyAgent(proxyUrl);
        
        https.globalAgent = httpsAgent;
        http.globalAgent = httpAgent;
        
        console.log('✅ [Proxy Setup] HTTP 全局代理 agent 已配置');
        
        // 为了兼容，也设置默认的 agent
        if (typeof global !== 'undefined') {
          global.httpsAgent = httpsAgent;
          global.httpAgent = httpAgent;
        }
      } catch (agentError) {
        console.log('⚠️  [Proxy Setup] 代理 agent 库未安装，仅使用环境变量方式');
      }
    } catch (error) {
      console.log('⚠️  [Proxy Setup] 传统 HTTP 代理配置失败');
    }

    console.log(`🌐 [Proxy Setup] 全局代理已启用: ${proxyUrl}`);
    console.log('💡 [Proxy Setup] 如需禁用代理，请设置 ENABLE_PROXY=false');
    console.log('🔥 [Proxy Setup] 所有 HTTP/HTTPS 请求将自动使用代理');
    
  } catch (error) {
    console.error('❌ [Proxy Setup] 代理配置失败:', error.message);
    console.log('🔄 [Proxy Setup] 将继续使用直连方式');
  }
}

// 立即执行代理设置
setupProxy();

module.exports = { setupProxy };