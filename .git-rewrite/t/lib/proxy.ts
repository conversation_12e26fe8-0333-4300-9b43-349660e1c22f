import { HttpsProxyAgent } from 'https-proxy-agent';
import { HttpProxyAgent } from 'http-proxy-agent';
import { SocksProxyAgent } from 'socks-proxy-agent';

interface ProxyConfig {
  enabled: boolean;
  http?: string;
  https?: string;
  socks?: string;
}

// 需要使用代理的域名列表
const PROXY_DOMAINS = [
  'oauth2.googleapis.com',
  'accounts.google.com',
  'www.googleapis.com',
  'github.com',
  'api.github.com',
  // 可以根据需要添加更多域名
];

// 获取代理配置
function getProxyConfig(): ProxyConfig {
  const isDev = process.env.NODE_ENV === 'development';
  
  // 在开发环境中启用代理，生产环境中禁用
  const shouldEnableProxy = isDev;
  
  const config = {
    enabled: shouldEnableProxy,
    http: process.env.HTTP_PROXY || process.env.http_proxy || 'http://127.0.0.1:7890',
    https: process.env.HTTPS_PROXY || process.env.https_proxy || 'http://127.0.0.1:7890',
    socks: process.env.ALL_PROXY || process.env.all_proxy || 'socks5://127.0.0.1:7890',
  };
  
  console.log('🔧 [Proxy Config]', {
    isDev,
    shouldEnableProxy,
    config
  });
  
  return config;
}

// 缓存配置以避免重复调用
let cachedConfig: ProxyConfig | null = null;

// 检查URL是否需要使用代理
function shouldUseProxy(url: string): boolean {
  if (!cachedConfig) {
    cachedConfig = getProxyConfig();
  }
  
  console.log('🔍 [Proxy Check]', { url, enabled: cachedConfig.enabled });
  
  if (!cachedConfig.enabled) {
    console.log('❌ [Proxy] Disabled for:', url);
    return false;
  }
  
  try {
    const urlObj = new URL(url);
    const needsProxy = PROXY_DOMAINS.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
    );
    
    console.log('🎯 [Proxy Check]', {
      url,
      hostname: urlObj.hostname,
      needsProxy,
      matchedDomains: PROXY_DOMAINS.filter(domain => 
        urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
      )
    });
    
    return needsProxy;
  } catch (error) {
    console.warn('⚠️ [Proxy] Invalid URL for proxy check:', url, error);
    return false;
  }
}

// 创建代理 Agent
export function createProxyAgent(url: string) {
  console.log('🔨 [createProxyAgent] Called with:', url);
  
  // 只为特定域名创建代理
  if (!shouldUseProxy(url)) {
    console.log('❌ [createProxyAgent] No proxy needed for:', url);
    return undefined;
  }

  const config = cachedConfig || getProxyConfig();
  
  try {
    const targetUrl = new URL(url);
    const protocol = targetUrl.protocol;

    console.log('🔧 [createProxyAgent] Creating agent for:', {
      url,
      protocol,
      config: {
        http: config.http,
        https: config.https
      }
    });

    if (protocol === 'https:' && config.https) {
      console.log(`✅ [createProxyAgent] Created HTTPS proxy agent for: ${url}`);
      const agent = new HttpsProxyAgent(config.https);
      console.log('🔗 [createProxyAgent] Agent created:', agent);
      return agent;
    } else if (protocol === 'http:' && config.http) {
      console.log(`✅ [createProxyAgent] Created HTTP proxy agent for: ${url}`);
      const agent = new HttpProxyAgent(config.http);
      console.log('🔗 [createProxyAgent] Agent created:', agent);
      return agent;
    } else {
      console.log('❌ [createProxyAgent] No matching proxy config for:', { protocol, config });
    }
  } catch (error) {
    console.error('❌ [createProxyAgent] Failed to create proxy agent:', error);
  }

  console.log('❌ [createProxyAgent] Returning undefined for:', url);
  return undefined;
}

// 创建支持代理的 fetch 函数
export function createProxyFetch() {
  console.log('🚀 [createProxyFetch] Creating proxy fetch function');
  
  return async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
    
    console.log('📡 [ProxyFetch] Making request to:', url);
    
    const agent = createProxyAgent(url);
    
    if (agent) {
      const modifiedInit = {
        ...init,
        // @ts-ignore - Node.js fetch supports agent option
        agent,
      };
      
      console.log('🌐 [ProxyFetch] Using proxy agent for:', url);
      
      try {
        const response = await fetch(input, modifiedInit);
        console.log('✅ [ProxyFetch] Proxy request successful:', {
          url,
          status: response.status,
          statusText: response.statusText
        });
        return response;
      } catch (error) {
        console.error('❌ [ProxyFetch] Proxy request failed:', {
          url,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined
        });
        throw error;
      }
    }
    
    // 对于不需要代理的请求，直接使用原生 fetch
    console.log('⚡ [ProxyFetch] Using direct fetch for:', url);
    try {
      const response = await fetch(input, init);
      console.log('✅ [ProxyFetch] Direct request successful:', {
        url,
        status: response.status,
        statusText: response.statusText
      });
      return response;
    } catch (error) {
      console.error('❌ [ProxyFetch] Direct request failed:', {
        url,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  };
}

// 为 NextAuth 提供代理配置
export function getNextAuthProxyConfig() {
  console.log('🔐 [NextAuth Proxy] Getting proxy config for NextAuth');
  
  const config = getProxyConfig();
  
  if (!config.enabled) {
    console.log('❌ [NextAuth Proxy] Proxy disabled, returning empty config');
    return {};
  }

  // NextAuth 需要为所有认证相关请求设置代理
  const httpAgent = config.http ? new HttpProxyAgent(config.http) : undefined;
  const httpsAgent = config.https ? new HttpsProxyAgent(config.https) : undefined;
  
  const nextAuthConfig = {
    httpOptions: {
      agent: {
        http: httpAgent,
        https: httpsAgent,
      },
    },
  };
  
  console.log('✅ [NextAuth Proxy] Created NextAuth proxy config:', {
    hasHttpAgent: !!httpAgent,
    hasHttpsAgent: !!httpsAgent,
    httpProxy: config.http,
    httpsProxy: config.https,
    config: nextAuthConfig
  });
  
  return nextAuthConfig;
}

// 全局设置代理（用于开发环境）
export function setupGlobalProxy() {
  const config = getProxyConfig();
  
  if (!config.enabled) {
    return;
  }

  // 设置全局代理环境变量
  if (config.http) {
    process.env.HTTP_PROXY = config.http;
    process.env.http_proxy = config.http;
  }
  
  if (config.https) {
    process.env.HTTPS_PROXY = config.https;
    process.env.https_proxy = config.https;
  }
  
  if (config.socks) {
    process.env.ALL_PROXY = config.socks;
    process.env.all_proxy = config.socks;
  }

  console.log('🌐 Proxy configuration applied:', {
    http: config.http,
    https: config.https,
    socks: config.socks,
  });
}
