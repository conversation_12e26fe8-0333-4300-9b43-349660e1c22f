{"name": "imageupscaler", "version": "1.0.0", "private": true, "author": "<PERSON><PERSON><PERSON>", "homepage": "https://imageupscaler.run", "scripts": {"dev": "NODE_NO_WARNINGS=1 next dev", "dev:proxy": "NODE_NO_WARNINGS=1 node scripts/dev-with-proxy.js", "dev:china": "NODE_NO_WARNINGS=1 ENABLE_PROXY=true PROXY_URL=http://127.0.0.1:7890 next dev", "build": "next build", "start": "NODE_NO_WARNINGS=1 next start", "lint": "next lint", "analyze": "ANALYZE=true pnpm build", "cf:build": "npx @cloudflare/next-on-pages", "cf:preview": "pnpm cf:build && wrangler pages dev", "cf:deploy": "pnpm cf:build && wrangler pages deploy"}, "dependencies": {"@ai-sdk/openai": "^1.0.18", "@ai-sdk/openai-compatible": "^0.0.17", "@ai-sdk/provider": "^1.0.4", "@ai-sdk/provider-utils": "^2.0.7", "@ai-sdk/replicate": "^0.0.2", "@aws-sdk/client-s3": "^3.740.0", "@aws-sdk/lib-storage": "^3.740.0", "@devnomic/marquee": "^1.0.2", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.3", "@next/third-parties": "^15.1.2", "@openpanel/nextjs": "^1.0.7", "@openrouter/ai-sdk-provider": "^0.0.6", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@stripe/stripe-js": "^5.4.0", "@supabase/supabase-js": "^2.47.10", "@types/canvas-confetti": "^1.9.0", "@types/mdx": "^2.0.13", "@vercel/analytics": "^1.5.0", "ai": "^4.0.33", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-fade": "^8.5.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.15.0", "google-one-tap": "^1.0.6", "highlight.js": "^11.11.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "jszip": "^3.10.1", "lucide-react": "^0.439.0", "markdown-it": "^14.1.0", "moment": "^2.30.1", "next": "14.2.9", "next-auth": "5.0.0-beta.20", "next-intl": "^3.26.3", "next-themes": "^0.4.4", "openai": "^4.78.1", "react": "^18", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icon-cloud": "^4.1.4", "react-icons": "^5.4.0", "react-slider": "^1.2.3", "react-tweet": "^3.2.1", "simple-flakeid": "^0.0.5", "socks-proxy-agent": "^8.0.5", "sonner": "^1.7.1", "stripe": "^17.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@cloudflare/next-on-pages": "^1.13.7", "@next/bundle-analyzer": "^15.1.3", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^18", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vercel": "39.1.1"}}