import { getUserValidCredits, insertCredit } from "@/models/credit";
import { Credit } from "@/types/credit";
import { User, UserCredits } from "@/types/user";
import { getSupabaseClient } from "@/models/db";
import { getFirstPaidOrderByUserUuid } from "@/models/order";
import { getIsoTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  Usage = "usage", // credits used for operations
  DailyReset = "daily_reset", // daily reset for free users
  MonthlyReset = "monthly_reset", // monthly reset for paid users
}

export enum CreditsAmount {
  NewUserGet = 5, // Free users get 5 daily credits
  PingCost = 1,
  ImageUpscale = 1, // 1 credit per image upscale
}

/**
 * Update user credits and log the transaction
 */
export async function updateUserCredits(
  user_id: string,
  credits_change: number,
  transaction_type: string,
  description?: string,
  task_id?: string
): Promise<number> {
  const supabase = getSupabaseClient();

  // Call the database function
  const { data, error } = await supabase.rpc('update_user_credits', {
    p_user_id: user_id,
    p_credits_change: credits_change,
    p_transaction_type: transaction_type,
    p_description: description,
    p_task_id: task_id
  });

  if (error) {
    throw error;
  }

  return data; // Returns new balance
}

/**
 * Check if user can use credits for an operation
 */
export async function canUseCredits(
  user_id: string,
  credits_needed: number,
  operation_type: string = 'image_upscale'
): Promise<boolean> {
  const supabase = getSupabaseClient();

  // Try to use the database function first
  const { data, error } = await supabase.rpc('can_use_credits', {
    p_user_id: user_id,
    p_credits_needed: credits_needed,
    p_operation_type: operation_type
  });

  if (error) {
    console.error('Error checking credits with RPC:', error);
    
    // Fallback to manual check
    const { data: userData } = await supabase
      .from('users')
      .select('credits_balance, subscription_tier')
      .eq('id', user_id)
      .single();

    if (!userData) {
      return false;
    }

    return userData.credits_balance >= credits_needed;
  }

  return data;
}

/**
 * Get user's current credits balance
 */
export async function getUserCreditsBalance(user_id: string): Promise<number> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase
    .from('users')
    .select('credits_balance')
    .eq('id', user_id)
    .single();

  if (error || !data) {
    return 0;
  }

  return data.credits_balance || 0;
}

/**
 * Legacy function for compatibility - Get user credits by UUID
 */
export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    const supabase = getSupabaseClient();
    
    // Get user by UUID (legacy compatibility)
    const { data: user } = await supabase
      .from('users')
      .select('id, credits_balance, subscription_tier')
      .eq('uuid', user_uuid)
      .single();

    if (user) {
      user_credits.left_credits = user.credits_balance || 0;
      user_credits.is_pro = user.subscription_tier !== 'free';
      
      // Check if user has made any payments
      const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
      if (first_paid_order) {
        user_credits.is_recharged = true;
      }
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

/**
 * Legacy function for compatibility - Decrease credits
 */
export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    const supabase = getSupabaseClient();
    
    // Get user by UUID
    const { data: user } = await supabase
      .from('users')
      .select('id')
      .eq('uuid', user_uuid)
      .single();

    if (user) {
      await updateUserCredits(
        user.id,
        -credits,
        trans_type,
        `Credits used: ${credits}`,
        undefined
      );
    }
  } catch (e) {
    console.log("decrease credits failed: ", e);
    throw e;
  }
}

/**
 * Legacy function for compatibility - Increase credits
 */
export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const supabase = getSupabaseClient();
    
    // Get user by UUID
    const { data: user } = await supabase
      .from('users')
      .select('id')
      .eq('uuid', user_uuid)
      .single();

    if (user) {
      await updateUserCredits(
        user.id,
        credits,
        trans_type,
        `Credits added: ${credits}`,
        undefined
      );
    }
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

/**
 * Reset daily credits for free users (called by cron job)
 */
export async function resetDailyCredits(): Promise<number> {
  const supabase = getSupabaseClient();

  const { data, error } = await supabase.rpc('reset_daily_credits');

  if (error) {
    throw error;
  }

  return data; // Returns number of affected users
}

/**
 * Get user's credit transaction history
 */
export async function getUserCreditHistory(
  user_id: string,
  page: number = 1,
  limit: number = 50
): Promise<Credit[]> {
  const supabase = getSupabaseClient();
  const offset = (page - 1) * limit;

  const { data, error } = await supabase
    .from('credits')
    .select('*')
    .eq('user_id', user_id)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw error;
  }

  return data || [];
}
