const FEISHU_APP_ID = 'cli_a224bcd89738900d';
const FEISHU_APP_SECRET = 'QM1br0QzedgdxTZK3sZO0PrwlOcHmhGp';

interface FeishuTokenResponse {
  code: number;
  msg: string;
  tenant_access_token: string;
}

/**
 * Get Feishu access token
 */
async function doGetFeishuAccessToken(): Promise<string> {
  try {
    const response = await fetch('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        "app_id": FEISHU_APP_ID,
        "app_secret": FEISHU_APP_SECRET
      })
    });

    const data = await response.json() as FeishuTokenResponse;
    if (data.code === 0) {
      return data.tenant_access_token;
    } else {
      throw new Error(`Failed to get access token: ${data.msg}`);
    }
  } catch (error) {
    console.error('Error getting Feishu access token:', error);
    throw error;
  }
}

/**
 * Send message to <PERSON>ishu
 * @param message Message content to send
 */
export async function doSendFeishuMessage(message: string): Promise<void> {
  const feishuUrl = 'https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=user_id';
  const receiveId = 'f15fgb3a';

  try {
    const accessToken = await doGetFeishuAccessToken();
    
    const response = await fetch(feishuUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        content: JSON.stringify({ text: message }),
        msg_type: 'text',
        receive_id: receiveId
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to send message: ${response.statusText}`);
    }

    console.log('Message sent to Feishu successfully');
  } catch (error) {
    console.error('Failed to send Feishu message:', error);
    throw error;
  }
}

/**
 * Format contact form data for Feishu message
 * @param formData Form data
 */
export function formatContactMessageForFeishu(formData: {
  name: string;
  email: string;
  app: string;
  purpose: string;
  location: string;
  message: string;
}): string {
  const timestamp = new Date().toLocaleString('en-US', { 
    timeZone: 'UTC',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  return `
🔔 New Contact Form Submission

📝 **Basic Information**
• Name: ${formData.name}
• Email: ${formData.email}
• Location: ${formData.location || 'Not provided'}

🔧 **App Information**
• Related App: ${formData.app || 'Not selected'}
• Contact Purpose: ${formData.purpose || 'Not selected'}

💬 **Message Content**
${formData.message}

🕒 **Submission Time**
${timestamp}

---
From ImageUpscaler Contact Form
`.trim();
} 