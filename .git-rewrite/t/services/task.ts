import { Task, CreateTaskRequest, TaskProgress } from "@/types/task";
import { createTask, updateTaskStatus, addExternalTaskId, getTaskById } from "@/models/task";
import { updateUserCredits } from "@/services/credit";

interface TaskMonitorCallbacks {
  onProgress?: (progress: TaskProgress) => void;
  onComplete?: (result: { task: Task; urls: string[] }) => void;
  onError?: (error: string) => void;
}

class TaskService {
  private readonly BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL!;
  private monitoringTasks = new Map<string, NodeJS.Timeout>();

  /**
   * Create a new image upscaling task
   */
  async createUpscaleTask(
    userId: string,
    imageUrls: string[],
    scale: number = 2,
    taskType: 'single' | 'batch' = 'single'
  ): Promise<Task> {
    // Calculate credits cost (1 credit per image)
    const creditsCost = imageUrls.length;

    // Create task record in database
    const task = await createTask({
      user_id: userId,
      task_type: taskType,
      input_urls: imageUrls,
      scale,
      credits_cost: creditsCost
    });

    // Deduct credits from user
    await updateUserCredits(userId, -creditsCost, 'usage', `Image upscaling task: ${task.id}`, task.id);

    // Start processing images
    await this.processTaskImages(task);

    return task;
  }

  /**
   * Process images for a task using the backend service
   */
  private async processTaskImages(task: Task): Promise<void> {
    try {
      // Update task status to processing
      await updateTaskStatus(task.id, 'processing');

      const externalTaskIds: string[] = [];

      // Process each image
      for (let i = 0; i < task.input_urls.length; i++) {
        const imageUrl = task.input_urls[i];
        
        try {
          // Call backend API to create external task
          const response = await fetch(`${this.BACKEND_API_URL}/task`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${process.env.BACKEND_API_KEY!}`
            },
            body: JSON.stringify({
              imageUrl,
              scale: task.scale
            })
          });

          if (!response.ok) {
            throw new Error(`Failed to create external task: ${response.statusText}`);
          }

          const result = await response.json();
          externalTaskIds.push(result.taskId);
          
          // Add external task ID to our task record
          await addExternalTaskId(task.id, result.taskId);

        } catch (error) {
          console.error(`Failed to process image ${i + 1}:`, error);
          // Continue with other images even if one fails
        }
      }

      // Start monitoring the external tasks
      this.monitorExternalTasks(task.id, externalTaskIds);

    } catch (error) {
      console.error('Failed to process task images:', error);
      await updateTaskStatus(task.id, 'failed', {
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Monitor external tasks for completion
   */
  private async monitorExternalTasks(taskId: string, externalTaskIds: string[]): Promise<void> {
    const checkInterval = 3000; // Check every 3 seconds
    const maxAttempts = 100; // Max 5 minutes
    let attempts = 0;

    const monitor = async () => {
      attempts++;

      try {
        const results: string[] = [];
        let completedCount = 0;
        let hasError = false;

        // Check status of each external task
        for (const externalTaskId of externalTaskIds) {
          try {
            const response = await fetch(`${this.BACKEND_API_URL}/task/${externalTaskId}`, {
              headers: {
                'Authorization': `Bearer ${process.env.BACKEND_API_KEY!}`
              }
            });

            if (response.ok) {
              const result = await response.json();
              
              if (result.status === 'FINISHED' && result.imageUrl) {
                results.push(result.imageUrl);
                completedCount++;
              } else if (result.status === 'FAILED') {
                hasError = true;
                completedCount++;
              }
            }
          } catch (error) {
            console.error(`Error checking external task ${externalTaskId}:`, error);
          }
        }

        // Update task progress
        const task = await getTaskById(taskId);
        if (task) {
          await updateTaskStatus(taskId, 'processing', {
            processed_images: completedCount,
            output_urls: results
          });
        }

        // Check if all tasks are complete
        if (completedCount >= externalTaskIds.length) {
          // Clear monitoring
          if (this.monitoringTasks.has(taskId)) {
            clearInterval(this.monitoringTasks.get(taskId)!);
            this.monitoringTasks.delete(taskId);
          }

          // Update final status
          const finalStatus = hasError && results.length === 0 ? 'failed' : 'completed';
          await updateTaskStatus(taskId, finalStatus, {
            processed_images: completedCount,
            output_urls: results,
            completed_at: new Date().toISOString()
          });

          return;
        }

        // Check if we've exceeded max attempts
        if (attempts >= maxAttempts) {
          // Clear monitoring
          if (this.monitoringTasks.has(taskId)) {
            clearInterval(this.monitoringTasks.get(taskId)!);
            this.monitoringTasks.delete(taskId);
          }

          await updateTaskStatus(taskId, 'failed', {
            error_message: 'Task monitoring timeout'
          });
        }

      } catch (error) {
        console.error('Error monitoring external tasks:', error);
      }
    };

    // Start monitoring
    const intervalId = setInterval(monitor, checkInterval);
    this.monitoringTasks.set(taskId, intervalId);

    // Initial check
    await monitor();
  }

  /**
   * Get task by ID
   */
  async getTask(taskId: string): Promise<Task | null> {
    return await getTaskById(taskId);
  }

  /**
   * Monitor a task with real-time callbacks
   */
  async monitorTask(taskId: string, callbacks: TaskMonitorCallbacks): Promise<void> {
    const checkInterval = 2000; // Check every 2 seconds
    
    const monitor = async () => {
      try {
        const task = await getTaskById(taskId);
        if (!task) {
          callbacks.onError?.('Task not found');
          return;
        }

        // Calculate progress
        const progressPercentage = task.total_images > 0 
          ? Math.round((task.processed_images / task.total_images) * 100)
          : 0;

        // Call progress callback
        callbacks.onProgress?.({
          task_id: taskId,
          status: task.status,
          processed_images: task.processed_images,
          total_images: task.total_images,
          progress_percentage: progressPercentage
        });

        // Handle completion
        if (task.status === 'completed') {
          callbacks.onComplete?.({
            task,
            urls: task.output_urls
          });
          return;
        }

        // Handle failure
        if (task.status === 'failed') {
          callbacks.onError?.(task.error_message || 'Task failed');
          return;
        }

        // Continue monitoring if still processing
        if (task.status === 'processing' || task.status === 'pending') {
          setTimeout(monitor, checkInterval);
        }

      } catch (error) {
        callbacks.onError?.(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    // Start monitoring
    await monitor();
  }

  /**
   * Cancel monitoring for a task
   */
  cancelMonitoring(taskId: string): void {
    if (this.monitoringTasks.has(taskId)) {
      clearInterval(this.monitoringTasks.get(taskId)!);
      this.monitoringTasks.delete(taskId);
    }
  }

  /**
   * Clean up all monitoring
   */
  cleanup(): void {
    this.monitoringTasks.forEach((intervalId, taskId) => {
      clearInterval(intervalId);
    });
    this.monitoringTasks.clear();
  }
}

export const taskService = new TaskService();