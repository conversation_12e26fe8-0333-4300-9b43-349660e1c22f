import { taskService } from "./task";
import { canUseCredits } from "./credit";
import { getCurrentUserInfo } from "@/lib/user-utils";

interface UpscaleResult {
  url: string;
  urls?: string[];
}

interface UpscaleCallbacks {
  onProgress?: (progress: number) => void;
  onComplete: (result: UpscaleResult) => void;
  onError: (error: string) => void;
}

interface TaskResponse {
  taskId: string;
  status: string;
}

export class UpscaleService {
  private eventSource: EventSource | null = null;
  private apiBaseUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL!;
  private token = process.env.BACKEND_API_KEY!;
  private simulateProgress(onProgress: (progress: number) => void) {
    let progress = 0;
    const interval = setInterval(() => {
      progress = Math.min(97, progress + Math.max(0.5, (97 - progress) * 0.1));
      onProgress(progress);
      if (progress >= 97) {
        clearInterval(interval);
      }
    }, 200);
    return () => clearInterval(interval);
  }

  async uploadImage(file: File): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${this.apiBaseUrl}/uploads/${file.name}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'accept': 'application/json'
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Upload error:', error);
      throw new Error('Failed to upload image');
    }
  }

  async createUpscaleTask(
    imageUrls: string | string[], 
    scale: number = 2,
    taskType: 'single' | 'batch' = 'single'
  ): Promise<TaskResponse> {
    const userInfo = await getCurrentUserInfo();
    if (!userInfo?.modernId) {
      throw new Error('User not authenticated');
    }

    const urls = Array.isArray(imageUrls) ? imageUrls : [imageUrls];
    const creditsNeeded = urls.length;

    // Check if user has enough credits
    const hasCredits = await canUseCredits(userInfo.modernId, creditsNeeded);
    if (!hasCredits) {
      throw new Error('Insufficient credits. Please upgrade your plan or wait for daily reset.');
    }

    // For batch processing, check subscription tier
    if (taskType === 'batch' || urls.length > 1) {
      if (!userInfo.can_use_batch) {
        throw new Error('Batch processing is only available for Pro and Explorer plans');
      }
    }

    // Create task using the new unified task service
    const task = await taskService.createUpscaleTask(
      userInfo.modernId,
      urls,
      scale,
      taskType
    );

    return { taskId: task.id, status: task.status };
  }



  monitorTaskStatus(
    taskId: string,
    callbacks: UpscaleCallbacks
  ): () => void {
    // Use the new task service for monitoring
    taskService.monitorTask(taskId, {
      onProgress: (progress) => {
        callbacks.onProgress?.(progress.progress_percentage);
      },
      onComplete: (result) => {
        callbacks.onComplete({
          url: result.urls[0] || '', // For compatibility
          urls: result.urls
        });
      },
      onError: callbacks.onError
    });

    // Return cleanup function
    return () => {
      taskService.cancelMonitoring(taskId);
    };
  }



  // New methods for batch processing
  async uploadAndUpscaleBatch(files: File[], scale: number = 2): Promise<TaskResponse> {
    const userInfo = await getCurrentUserInfo();
    if (!userInfo?.modernId) {
      throw new Error('User not authenticated');
    }

    // Check if user can use batch processing
    if (!userInfo.can_use_batch) {
      throw new Error('Batch processing is only available for Pro and Explorer plans');
    }

    // Upload all images
    const uploadPromises = files.map(file => this.uploadImage(file));
    const imageUrls = await Promise.all(uploadPromises);

    return this.createUpscaleTask(imageUrls, scale, 'batch');
  }

  async uploadAndUpscale(file: File, scale: number = 2): Promise<TaskResponse> {
    const imageUrl = await this.uploadImage(file);
    return this.createUpscaleTask(imageUrl, scale, 'single');
  }

  startUpscaleTask(
    imageId: string,
    scale: number,
    callbacks: UpscaleCallbacks
  ): () => void {
    const { onProgress, onComplete, onError } = callbacks;

    // Create EventSource for SSE connection
    this.eventSource = new EventSource(`/api/upscale/${imageId}?scale=${scale}`);

    let currentProgress = 0;

    this.eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      // Generate pseudo progress between 0-95% with random increments
      const newProgress = Math.min(currentProgress + Math.floor(Math.random() * 10 + 5), 95);
      currentProgress = newProgress;
      onProgress?.(newProgress);

      if (data.status === 'completed') {
        onComplete({ url: data.url });
        this.eventSource?.close();
      }
    };

    this.eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      onError('Failed to process image');
      this.eventSource?.close();
    };

    // Return cleanup function
    return () => {
      this.eventSource?.close();
    };
  }
}

// Create a singleton instance
export const upscaleService = new UpscaleService(); 