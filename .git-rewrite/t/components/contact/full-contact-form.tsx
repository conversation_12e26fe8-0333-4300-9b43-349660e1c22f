"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import AnalyticsUtils from "@/lib/analytics";

const appOptions = [
  { value: "image-upscaler", label: "Image Upscaler" },
  { value: "video-upscaler", label: "Video Upscaler" },
  { value: "ai-backgrounds", label: "AI Backgrounds" },
];

const purposeOptions = [
  { value: "question", label: "Question" },
  { value: "feedback", label: "Feedback" },
  { value: "bug-report", label: "Bug Report" },
  { value: "feature-request", label: "Feature Request" },
  { value: "other", label: "Other" },
];

export function FullContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    app: "",
    purpose: "",
    location: "",
    message: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Track form submission start event
      AnalyticsUtils.trackEvent('contact_form_submit_start', {
        app_selection: formData.app,
        purpose: formData.purpose,
        has_location: !!formData.location
      });

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Track successful submission event
        AnalyticsUtils.trackConversion('contact_form_submit', undefined, {
          app_selection: formData.app,
          purpose: formData.purpose,
          user_location: formData.location
        });

        toast.success(result.message || "Message sent successfully! We will get back to you soon.");
        
        // Reset form
        setFormData({
          name: "",
          email: "",
          app: "",
          purpose: "",
          location: "",
          message: "",
        });
      } else {
        throw new Error(result.error || "Failed to send message");
      }
    } catch (error: any) {
      // Track failure event
      AnalyticsUtils.trackEvent('contact_form_submit_error', {
        error_message: error.message,
        app_selection: formData.app,
        purpose: formData.purpose
      });

      console.error('Contact form error:', error);
      toast.error(error.message || "Failed to send message. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Track selection event
    AnalyticsUtils.trackInteraction('select', 'change', {
      field: name,
      value: value,
      form_type: 'contact'
    });
  };

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Contact</h1>
        <p className="text-xl text-muted-foreground">
          Contact us for any questions or feedback
        </p>
      </div>

      <div className="bg-card rounded-xl p-6 md:p-8 shadow-lg">
        <h2 className="text-2xl font-semibold mb-6">Send us a message</h2>
        <p className="text-muted-foreground mb-8">
          We will get back to you as soon as possible
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Your Name
              </label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder="Enter your name"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                Your Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                placeholder="Enter your email"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label htmlFor="app" className="text-sm font-medium">
                Which app?
              </label>
              <Select
                value={formData.app}
                onValueChange={(value) => handleSelectChange("app", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an app" />
                </SelectTrigger>
                <SelectContent>
                  {appOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label htmlFor="purpose" className="text-sm font-medium">
                What this is for?
              </label>
              <Select
                value={formData.purpose}
                onValueChange={(value) => handleSelectChange("purpose", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select purpose of contact" />
                </SelectTrigger>
                <SelectContent>
                  {purposeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="location" className="text-sm font-medium">
              Where are you using our App from?
            </label>
            <Input
              id="location"
              name="location"
              value={formData.location}
              onChange={handleChange}
              placeholder="Enter your location"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="message" className="text-sm font-medium">
              Your Message
            </label>
            <Textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              required
              placeholder="Enter your message"
              rows={6}
            />
          </div>

          <Button
            type="submit"
            className="w-full md:w-auto"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <span className="mr-2">Sending...</span>
                <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              </>
            ) : (
              "Send Message"
            )}
          </Button>
        </form>
      </div>

      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold mb-4">
          Upscale your design easily with AI
        </h2>
        <p className="text-muted-foreground">
          © Copyright {new Date().getFullYear()} imageUpscaler.run. All Rights Reserved.
        </p>
      </div>
    </div>
  );
} 