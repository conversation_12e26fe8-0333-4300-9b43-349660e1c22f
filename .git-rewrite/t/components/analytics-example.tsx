'use client';

import { Button } from '@/components/ui/button';
import AnalyticsUtils from '@/lib/analytics';

/**
 * Example component demonstrating Analytics usage
 * This is just an example - you can remove this file if not needed
 */
export function AnalyticsExample() {
  const handleButtonClick = () => {
    // Track button click event
    AnalyticsUtils.trackInteraction('button', 'click', {
      location: 'analytics_example',
      button_type: 'primary'
    });
    
    console.log('Button clicked and tracked!');
  };

  const handleConversion = () => {
    // Track conversion event
    AnalyticsUtils.trackConversion('signup', undefined, {
      source: 'analytics_example'
    });
    
    console.log('Conversion tracked!');
  };

  const handleCustomEvent = () => {
    // Track custom event
    AnalyticsUtils.trackEvent('custom_action', {
      action_type: 'demo',
      timestamp: new Date().toISOString()
    });
    
    console.log('Custom event tracked!');
  };

  return (
    <div className="p-6 space-y-4 border rounded-lg">
      <h3 className="text-lg font-semibold">Analytics Example</h3>
      <p className="text-sm text-muted-foreground">
        This component demonstrates how to use Vercel Analytics with custom event tracking.
      </p>
      
      <div className="space-x-2">
        <Button onClick={handleButtonClick}>
          Track Button Click
        </Button>
        
        <Button variant="outline" onClick={handleConversion}>
          Track Conversion
        </Button>
        
        <Button variant="secondary" onClick={handleCustomEvent}>
          Track Custom Event
        </Button>
      </div>
      
      <div className="text-xs text-muted-foreground">
        <p>Check your browser console to see the tracking confirmations.</p>
        <p>Events will appear in your Vercel Analytics dashboard when deployed.</p>
      </div>
    </div>
  );
} 