export interface BatchUploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  preview: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  resultUrl?: string;
  error?: string;
}

export type BatchProcessingStatus = 'idle' | 'processing' | 'completed' | 'error';

export interface BatchStatusSummary {
  pending: number;
  processing: number;
  completed: number;
  error: number;
  total: number;
}

export interface BatchProcessingOptions {
  scale: number;
  quality?: 'standard' | 'high';
  format?: 'original' | 'png' | 'jpg';
}
