import { useState, useRef, useEffect } from 'react';

interface ImageCompareProps {
  original: string;
  processed?: string;
  onDownload: () => void;
  isProcessing?: boolean;
  progress?: number;
}

export const ImageCompare = ({
  original,
  processed,
  onDownload,
  isProcessing,
  progress = 0
}: ImageCompareProps) => {
  const [position, setPosition] = useState(50);
  const [internalProgress, setInternalProgress] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);
  const progressInterval = useRef<NodeJS.Timeout>();

  // 模拟进度增长
  useEffect(() => {
    if (isProcessing && !processed) {
      // 重置进度
      setInternalProgress(0);
      
      // 创建进度增长的定时器
      progressInterval.current = setInterval(() => {
        setInternalProgress(prev => {
          // 如果已经达到97%，就停止增长
          if (prev >= 97) {
            clearInterval(progressInterval.current);
            return 97;
          }
          // 根据当前进度动态计算增长速度
          const increment = Math.max(0.5, (97 - prev) * 0.1);
          return Math.min(97, prev + increment);
        });
      }, 200);

      return () => {
        if (progressInterval.current) {
          clearInterval(progressInterval.current);
        }
      };
    } else if (!isProcessing && processed) {
      // 处理完成时，显示100%
      setInternalProgress(100);
    }
  }, [isProcessing, processed]);

  const handleMove = (event: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging.current || !containerRef.current || isProcessing) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const x = 'touches' in event 
      ? event.touches[0].clientX 
      : event.clientX;
    const position = ((x - containerRect.left) / containerRect.width) * 100;
    
    setPosition(Math.min(Math.max(0, position), 100));
  };

  const handleMouseDown = () => {
    if (!isProcessing) {
      isDragging.current = true;
    }
  };

  const handleMouseUp = () => {
    isDragging.current = false;
  };

  useEffect(() => {
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchend', handleMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchend', handleMouseUp);
    };
  }, []);

  return (
    <div className="relative w-full rounded-2xl overflow-hidden bg-white border border-gray-200 shadow-lg">
      {/* Enhanced Labels */}
      <div className="absolute top-4 left-4 z-20 bg-white/95 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-full text-sm font-semibold shadow-lg border border-gray-200">
        <span className="flex items-center gap-2">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          Before
        </span>
      </div>
      <div className="absolute top-4 right-4 z-20 bg-white/95 backdrop-blur-sm text-gray-800 px-4 py-2 rounded-full text-sm font-semibold shadow-lg border border-gray-200">
        <span className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          After
        </span>
      </div>

      <div 
        ref={containerRef}
        className="relative select-none aspect-[4/3] w-full"
        onMouseMove={handleMove}
        onTouchMove={handleMove}
      >
        {/* 原始图片 */}
        <img
          src={original}
          alt="Original"
          className="absolute inset-0 w-full h-full object-cover"
        />

        {isProcessing ? (
          // Enhanced processing overlay
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-purple-500/20 to-blue-600/20 backdrop-blur-sm flex items-center justify-center">
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border border-white/50 max-w-sm mx-4">
              {/* AI Processing Icon */}
              <div className="flex justify-center mb-4">
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center animate-pulse">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div className="absolute -inset-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl opacity-20 animate-ping"></div>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="relative h-3 w-full overflow-hidden rounded-full bg-gray-200 mb-4">
                <div 
                  className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-300 rounded-full"
                  style={{ width: `${internalProgress}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-pulse" />
                </div>
              </div>
              
              {/* Status Text */}
              <div className="text-center">
                <p className="text-lg font-semibold text-gray-900 mb-1">
                  AI Enhancement in Progress
                </p>
                <p className="text-sm text-gray-600 mb-2">
                  Using advanced algorithms to upscale your image
                </p>
                <p className="text-lg font-bold text-blue-600">
                  {Math.round(internalProgress)}%
                </p>
              </div>
            </div>
          </div>
        ) : processed ? (
          // 处理完成后的对比效果
          <>
            <div
              className="absolute inset-0"
              style={{ clipPath: `inset(0 ${100 - position}% 0 0)` }}
            >
              <img
                src={original}
                alt="Original"
                className="w-full h-full object-cover"
              />
            </div>
            <img
              src={processed}
              alt="Processed"
              className="absolute inset-0 w-full h-full object-cover"
            />
            {/* Enhanced Slider */}
            <div
              className="absolute top-0 bottom-0 z-10 group"
              style={{ left: `${position}%` }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleMouseDown}
            >
              {/* Divider Line */}
              <div className="absolute inset-y-0 -left-0.5 w-1 bg-white shadow-lg" />
              
              {/* Slider Handle */}
              <div className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-xl border-2 border-gray-200 flex items-center justify-center cursor-grab active:cursor-grabbing transition-all duration-200 group-hover:scale-110 group-active:scale-95">
                <div className="flex items-center gap-0.5">
                  <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M15 19l-7-7 7-7" />
                  </svg>
                  <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
              
              {/* Slider Instruction */}
              <div className="absolute top-full mt-2 -translate-x-1/2 bg-black/75 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Drag to compare
              </div>
            </div>
          </>
        ) : null}
      </div>

      {processed && !isProcessing && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2">
          <button
            onClick={onDownload}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center gap-3 font-semibold group transform hover:scale-105 active:scale-95"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            <span>Download Enhanced Image</span>
            <svg className="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
}; 