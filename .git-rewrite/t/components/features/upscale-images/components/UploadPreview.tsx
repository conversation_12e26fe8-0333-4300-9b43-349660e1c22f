import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { UploadFile } from "../types";

interface UploadPreviewProps {
  upload: UploadFile;
  onRemove: (id: string) => void;
}

export const UploadPreview = ({ upload, onRemove }: UploadPreviewProps) => {
  const getStatusInfo = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return {
          progress: 0,
          text: 'Preparing...',
          progressClass: 'w-0 bg-blue-500/50',
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          icon: '⏳'
        };
      case 'uploading':
        return {
          progress: 50,
          text: 'Uploading...',
          progressClass: 'w-1/2 bg-blue-500 animate-pulse',
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-700',
          icon: '⬆️'
        };
      case 'done':
        return {
          progress: 100,
          text: 'Ready to process',
          progressClass: 'w-full bg-green-500',
          bgColor: 'bg-green-50',
          textColor: 'text-green-700',
          icon: '✅'
        };
      case 'error':
        return {
          progress: 100,
          text: 'Upload failed',
          progressClass: 'w-full bg-red-500',
          bgColor: 'bg-red-50',
          textColor: 'text-red-700',
          icon: '❌'
        };
      default:
        return {
          progress: 0,
          text: 'Unknown status',
          progressClass: 'w-0 bg-gray-400',
          bgColor: 'bg-gray-50',
          textColor: 'text-gray-600',
          icon: '❓'
        };
    }
  };

  const statusInfo = getStatusInfo(upload.status);

  return (
    <div className={`flex items-center gap-4 p-4 border border-gray-200 rounded-xl ${statusInfo.bgColor} transition-all duration-300`}>
      <div className="relative w-16 h-16 shrink-0">
        <img
          src={upload.preview}
          alt="Preview"
          className="w-full h-full object-cover rounded-lg shadow-sm"
        />
        {upload.status === 'done' && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-white shadow-lg border border-gray-200 hover:bg-gray-50"
            onClick={() => onRemove(upload.id)}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
        
        {/* Status icon overlay */}
        <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white shadow-sm flex items-center justify-center text-xs">
          {statusInfo.icon}
        </div>
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between gap-2 mb-2">
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-gray-900 truncate mb-0.5">{upload.file.name}</p>
            <p className={`text-xs font-medium ${statusInfo.textColor}`}>
              {statusInfo.text}
            </p>
          </div>
          <div className={`shrink-0 text-sm font-bold ${statusInfo.textColor}`}>
            {statusInfo.progress}%
          </div>
        </div>
        
        {/* Enhanced progress bar */}
        <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200">
          <div 
            className={`absolute left-0 top-0 h-full transition-all duration-500 rounded-full ${statusInfo.progressClass}`}
            style={{ width: `${statusInfo.progress}%` }}
          >
            {upload.status === 'uploading' && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
            )}
          </div>
        </div>
        
        {/* File size */}
        <div className="mt-1 text-xs text-gray-500">
          {(upload.file.size / 1024 / 1024).toFixed(1)} MB
        </div>
      </div>
    </div>
  );
}; 