"use client";

import { useState, useRef, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Upload, 
  X, 
  Download, 
  Eye, 
  Zap, 
  Crown, 
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from "lucide-react";
import { upscaleService } from "@/services/upscale";
import { cn } from "@/lib/utils";

interface BatchFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
  progress: number;
  resultUrl?: string;
  error?: string;
}

interface BatchUploaderProps {
  maxFiles?: number;
  maxFileSize?: number; // in MB
  allowedTypes?: string[];
}

export function BatchUpscaler({ 
  maxFiles = 100, 
  maxFileSize = 10,
  allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
}: BatchUploaderProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [files, setFiles] = useState<BatchFile[]>([]);
  const [scale, setScale] = useState(2);
  const [isProcessing, setIsProcessing] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [taskId, setTaskId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check if user can use batch processing
  const canUseBatch = session?.user?.can_use_batch || false;
  const userTier = session?.user?.subscription_tier || 'free';
  const creditsBalance = session?.user?.credits_balance || 0;

  const handleFileSelect = useCallback((selectedFiles: FileList) => {
    const newFiles: BatchFile[] = [];
    
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      
      // Check file type
      if (!allowedTypes.includes(file.type)) {
        toast.error(`${file.name} is not a supported image format`);
        continue;
      }
      
      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        toast.error(`${file.name} is too large (max ${maxFileSize}MB)`);
        continue;
      }
      
      // Check if we're at max files
      if (files.length + newFiles.length >= maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`);
        break;
      }
      
      const batchFile: BatchFile = {
        id: `${Date.now()}-${i}`,
        file,
        preview: URL.createObjectURL(file),
        status: 'pending',
        progress: 0
      };
      
      newFiles.push(batchFile);
    }
    
    setFiles(prev => [...prev, ...newFiles]);
  }, [files.length, maxFiles, maxFileSize, allowedTypes]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const removeFile = useCallback((id: string) => {
    setFiles(prev => {
      const updated = prev.filter(f => f.id !== id);
      // Revoke object URL to prevent memory leak
      const removed = prev.find(f => f.id === id);
      if (removed) {
        URL.revokeObjectURL(removed.preview);
      }
      return updated;
    });
  }, []);

  const startBatchProcessing = async () => {
    if (!canUseBatch) {
      toast.error('Batch processing is only available for Pro and Explorer plans');
      return;
    }

    if (files.length === 0) {
      toast.error('Please select at least one image');
      return;
    }

    if (creditsBalance < files.length) {
      toast.error(`Insufficient credits. You need ${files.length} credits but only have ${creditsBalance}`);
      return;
    }

    setIsProcessing(true);
    setOverallProgress(0);

    try {
      // Start batch processing
      const response = await upscaleService.uploadAndUpscaleBatch(
        files.map(f => f.file), 
        scale
      );
      
      setTaskId(response.taskId);
      
      // Monitor progress
      const cleanup = upscaleService.monitorTaskStatus(response.taskId, {
        onProgress: (progress) => {
          setOverallProgress(progress);
          
          // Update individual file progress (simulate based on overall progress)
          const completedCount = Math.floor((progress / 100) * files.length);
          setFiles(prev => prev.map((file, index) => {
            if (index < completedCount) {
              return { ...file, status: 'completed', progress: 100 };
            } else if (index === completedCount) {
              return { ...file, status: 'processing', progress: progress % (100 / files.length) };
            }
            return file;
          }));
        },
        onComplete: (result) => {
          setFiles(prev => prev.map((file, index) => ({
            ...file,
            status: 'completed',
            progress: 100,
            resultUrl: result.urls?.[index] || ""
          })));
          setOverallProgress(100);
          setIsProcessing(false);
          toast.success('Batch processing completed!');
        },
        onError: (error) => {
          setFiles(prev => prev.map(file => ({
            ...file,
            status: 'failed',
            error: error
          })));
          setIsProcessing(false);
          toast.error(`Processing failed: ${error}`);
        }
      });

      // Store cleanup function for potential cancellation
      return cleanup;
    } catch (error) {
      setIsProcessing(false);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to start batch processing: ${errorMessage}`);
    }
  };

  const downloadAll = () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.resultUrl);
    if (completedFiles.length === 0) {
      toast.error('No completed files to download');
      return;
    }

    completedFiles.forEach((file, index) => {
      setTimeout(() => {
        const a = document.createElement('a');
        a.href = file.resultUrl!;
        a.download = `upscaled-${file.file.name}`;
        a.click();
      }, index * 500); // Stagger downloads
    });
  };

  const getStatusIcon = (status: BatchFile['status']) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-gray-500" />;
      case 'uploading': return <Upload className="w-4 h-4 text-blue-500" />;
      case 'processing': return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: BatchFile['status']) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-700';
      case 'uploading': return 'bg-blue-100 text-blue-700';
      case 'processing': return 'bg-blue-100 text-blue-700';
      case 'completed': return 'bg-green-100 text-green-700';
      case 'failed': return 'bg-red-100 text-red-700';
    }
  };

  if (!canUseBatch) {
    return (
      <Card className="p-8 text-center">
        <Crown className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Batch Processing</h3>
        <p className="text-muted-foreground mb-4">
          Batch processing is available for Pro and Explorer plans
        </p>
        <Badge variant="outline" className="mb-4">
          Current plan: {userTier}
        </Badge>
        <div className="space-y-3">
          <Button 
            onClick={() => router.push('/pricing')}
            className="w-full"
          >
            <Zap className="w-4 h-4 mr-2" />
            Upgrade to Pro
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Batch Image Upscaler</h2>
          <p className="text-muted-foreground">
            Upload multiple images and process them all at once
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-muted-foreground">Credits available</div>
          <div className="text-xl font-bold">{creditsBalance}</div>
        </div>
      </div>

      {/* Scale Selection */}
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <label className="text-sm font-medium">Scale Factor:</label>
          <div className="flex gap-2">
            {[2, 4, 8].map(scaleOption => (
              <Button
                key={scaleOption}
                variant={scale === scaleOption ? "default" : "outline"}
                size="sm"
                onClick={() => setScale(scaleOption)}
                disabled={isProcessing}
              >
                {scaleOption}x
              </Button>
            ))}
          </div>
          <div className="text-sm text-muted-foreground">
            {files.length} images × {scale}x = {files.length} credits
          </div>
        </div>
      </Card>

      {/* Upload Area */}
      <Card 
        className={cn(
          "border-2 border-dashed transition-colors",
          isProcessing ? "border-gray-200 bg-gray-50" : "border-gray-300 hover:border-gray-400"
        )}
      >
        <div
          className="p-8 text-center"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Upload Images</h3>
          <p className="text-muted-foreground mb-4">
            Drag and drop images here, or click to select
          </p>
          <div className="flex flex-wrap gap-2 justify-center text-sm text-muted-foreground mb-4">
            <span>Supported: JPEG, PNG, WebP</span>
            <span>•</span>
            <span>Max size: {maxFileSize}MB</span>
            <span>•</span>
            <span>Max files: {maxFiles}</span>
          </div>
          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={isProcessing}
            variant="outline"
          >
            Select Files
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={allowedTypes.join(',')}
            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            className="hidden"
          />
        </div>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">
              Selected Files ({files.length})
            </h3>
            <div className="flex gap-2">
              {!isProcessing && (
                <Button
                  onClick={() => setFiles([])}
                  variant="outline"
                  size="sm"
                >
                  Clear All
                </Button>
              )}
              {overallProgress === 100 && (
                <Button
                  onClick={downloadAll}
                  size="sm"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download All
                </Button>
              )}
            </div>
          </div>

          {/* Overall Progress */}
          {isProcessing && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">
                  {overallProgress.toFixed(0)}%
                </span>
              </div>
              <Progress value={overallProgress} className="h-2" />
            </div>
          )}

          {/* File Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {files.map((file) => (
              <div key={file.id} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(file.status)}
                    <span className="text-sm font-medium truncate">
                      {file.file.name}
                    </span>
                  </div>
                  {!isProcessing && (
                    <Button
                      onClick={() => removeFile(file.id)}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="aspect-video bg-gray-100 rounded mb-2 overflow-hidden">
                  <img
                    src={file.preview}
                    alt={file.file.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className={getStatusColor(file.status)}>
                    {file.status}
                  </Badge>
                  
                  {file.status === 'completed' && file.resultUrl && (
                    <div className="flex gap-1">
                      <Button
                        onClick={() => window.open(file.resultUrl, '_blank')}
                        variant="outline"
                        size="sm"
                        className="h-6 px-2"
                      >
                        <Eye className="w-3 h-3" />
                      </Button>
                      <Button
                        onClick={() => {
                          const a = document.createElement('a');
                          a.href = file.resultUrl!;
                          a.download = `upscaled-${file.file.name}`;
                          a.click();
                        }}
                        variant="outline"
                        size="sm"
                        className="h-6 px-2"
                      >
                        <Download className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>

                {file.status === 'failed' && file.error && (
                  <div className="text-xs text-red-600 mt-1">
                    {file.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Action Buttons */}
      {files.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {files.length} files selected • {files.length} credits required
            </div>
            <Button
              onClick={startBatchProcessing}
              disabled={isProcessing || files.length === 0 || creditsBalance < files.length}
              className="px-6"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Start Batch Processing
                </>
              )}
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}