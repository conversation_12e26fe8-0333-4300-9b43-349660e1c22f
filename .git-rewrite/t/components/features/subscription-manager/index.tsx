"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Crown, 
  CreditCard, 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle, 
  Settings,
  Download,
  Zap,
  Clock,
  BarChart3
} from "lucide-react";
import { SUBSCRIPTION_PLANS } from "@/models/subscription";
import { PricingV2 } from "@/components/blocks/pricing-v2";
import { format } from "date-fns";

interface SubscriptionManagerProps {
  onUpgrade?: (planId: string, interval: 'monthly' | 'yearly') => void;
}

export function SubscriptionManager({ onUpgrade }: SubscriptionManagerProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const subscription = session?.user?.subscription;
  const plan = session?.user?.plan || SUBSCRIPTION_PLANS.free;
  const creditsBalance = session?.user?.credits_balance || 0;
  const canUseBatch = session?.user?.can_use_batch || false;

  const handleUpgrade = async (planId: string, interval: 'monthly' | 'yearly') => {
    if (onUpgrade) {
      onUpgrade(planId, interval);
    } else {
      // Default behavior - redirect to checkout
      router.push(`/checkout?plan=${planId}&interval=${interval}`);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;
    
    setIsLoading(true);
    try {
      const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subscriptionId: subscription.id })
      });

      if (response.ok) {
        toast.success('Subscription cancelled successfully');
        router.refresh();
      } else {
        throw new Error('Failed to cancel subscription');
      }
    } catch (error) {
      toast.error('Failed to cancel subscription');
    } finally {
      setIsLoading(false);
    }
  };

  const getNextBillingDate = () => {
    if (!subscription) return null;
    return new Date(subscription.current_period_end);
  };

  const getBillingStatus = () => {
    if (!subscription) return null;
    
    const now = new Date();
    const periodEnd = new Date(subscription.current_period_end);
    
    if (subscription.cancel_at_period_end) {
      return 'canceling';
    }
    
    if (subscription.status === 'past_due') {
      return 'past_due';
    }
    
    if (periodEnd < now) {
      return 'expired';
    }
    
    return 'active';
  };

  const getCreditsUsagePercentage = () => {
    if (plan.id === 'free') {
      return (5 - creditsBalance) / 5 * 100;
    }
    
    const monthlyCredits = plan.credits.monthly || 0;
    return (monthlyCredits - creditsBalance) / monthlyCredits * 100;
  };

  const getRemainingDays = () => {
    if (!subscription) return null;
    
    const now = new Date();
    const periodEnd = new Date(subscription.current_period_end);
    const diffTime = periodEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  const billingStatus = getBillingStatus();
  const nextBillingDate = getNextBillingDate();
  const remainingDays = getRemainingDays();
  const creditsUsagePercentage = getCreditsUsagePercentage();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Subscription Management</h2>
          <p className="text-muted-foreground">
            Manage your subscription and billing
          </p>
        </div>
        <Badge variant={plan.id === 'free' ? 'secondary' : 'default'}>
          {plan.name}
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="upgrade">Upgrade</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="w-5 h-5" />
                Current Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold">{plan.name}</h3>
                  <p className="text-muted-foreground">{plan.description}</p>
                </div>
                {subscription && (
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">
                      {subscription.billing_interval === 'year' ? 'Annual' : 'Monthly'}
                    </div>
                    <div className="text-lg font-bold">
                      ${(subscription.price_amount / 100).toFixed(2)}
                    </div>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{creditsBalance}</div>
                  <div className="text-sm text-muted-foreground">Credits Available</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {canUseBatch ? 'Yes' : 'No'}
                  </div>
                  <div className="text-sm text-muted-foreground">Batch Processing</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {plan.id === 'free' ? 'Daily' : 'Monthly'}
                  </div>
                  <div className="text-sm text-muted-foreground">Reset Period</div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Plan Features:</h4>
                <ul className="space-y-1">
                  {plan.features.map((feature: string, index: number) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {billingStatus && (
                <div className="mt-4 p-3 rounded-lg bg-blue-50 border border-blue-200">
                  <div className="flex items-center gap-2">
                    {billingStatus === 'active' && <CheckCircle className="w-4 h-4 text-green-500" />}
                    {billingStatus === 'canceling' && <AlertCircle className="w-4 h-4 text-yellow-500" />}
                    {billingStatus === 'past_due' && <AlertCircle className="w-4 h-4 text-red-500" />}
                    {billingStatus === 'expired' && <AlertCircle className="w-4 h-4 text-red-500" />}
                    
                    <span className="text-sm font-medium">
                      {billingStatus === 'active' && 'Subscription Active'}
                      {billingStatus === 'canceling' && 'Canceling at Period End'}
                      {billingStatus === 'past_due' && 'Payment Past Due'}
                      {billingStatus === 'expired' && 'Subscription Expired'}
                    </span>
                  </div>
                  
                  {nextBillingDate && remainingDays && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {billingStatus === 'active' ? 'Next billing' : 'Period ends'}: {' '}
                      {format(nextBillingDate, 'PPP')} ({remainingDays} days)
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          {/* Usage Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Usage Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Credits Used</span>
                    <span className="text-sm text-muted-foreground">
                      {plan.id === 'free' ? 
                        `${5 - creditsBalance} / 5 daily` : 
                        `${(plan.credits.monthly || 0) - creditsBalance} / ${plan.credits.monthly || 0} monthly`
                      }
                    </span>
                  </div>
                  <Progress value={creditsUsagePercentage} className="h-2" />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">
                      {plan.id === 'free' ? 5 - creditsBalance : (plan.credits.monthly || 0) - creditsBalance}
                    </div>
                    <div className="text-sm text-muted-foreground">Credits Used</div>
                  </div>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{creditsBalance}</div>
                    <div className="text-sm text-muted-foreground">Credits Remaining</div>
                  </div>
                </div>

                {plan.id === 'free' && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium">Daily Reset</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      Your credits reset daily at UTC midnight
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          {/* Billing Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Billing Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {subscription ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-muted-foreground">Current Plan</div>
                      <div className="font-medium">{plan.name}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Billing Cycle</div>
                      <div className="font-medium">
                        {subscription.billing_interval === 'year' ? 'Annual' : 'Monthly'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Amount</div>
                      <div className="font-medium">
                        ${(subscription.price_amount / 100).toFixed(2)} / {subscription.billing_interval}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">Status</div>
                      <div className="font-medium capitalize">{subscription.status}</div>
                    </div>
                  </div>

                  {nextBillingDate && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="text-sm text-muted-foreground">
                        Next billing date
                      </div>
                      <div className="font-medium">
                        {format(nextBillingDate, 'PPP')}
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Download Invoice
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4 mr-2" />
                      Update Payment Method
                    </Button>
                  </div>

                  {!subscription.cancel_at_period_end && (
                    <Button 
                      variant="destructive" 
                      onClick={handleCancelSubscription}
                      disabled={isLoading}
                    >
                      Cancel Subscription
                    </Button>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Crown className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Active Subscription</h3>
                  <p className="text-muted-foreground mb-4">
                    You're currently on the free plan
                  </p>
                  <Button onClick={() => setActiveTab('upgrade')}>
                    <Zap className="w-4 h-4 mr-2" />
                    Upgrade Now
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upgrade" className="space-y-4">
          {/* Upgrade Options */}
          <PricingV2 
            onSelectPlan={handleUpgrade}
            currentPlan={plan.id}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}