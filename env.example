# NextAuth 配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-here

# Google OAuth 配置
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
NEXT_PUBLIC_AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

# Google One Tap (可选)
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED=true

# GitHub OAuth (可选)
NEXT_PUBLIC_AUTH_GITHUB_ENABLED=false
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

# Supabase 配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Stripe 配置
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# 后端 API 配置
NEXT_PUBLIC_BACKEND_API_URL=https://new-iu-cf.your-subdomain.workers.dev
BACKEND_API_KEY=your-backend-api-key

# 其他配置
NEXT_PUBLIC_API_URL=/api