# AI Image Upscaler SaaS

Transform your images with AI-powered upscaling technology. Enhance photos to stunning 4K/8K quality while preserving natural details.

![preview](preview.png)

## 🚀 Quick Start

1. Clone the repository

```bash
git clone <your-repository-url>
cd image-upscaler
```

2. Install dependencies

```bash
pnpm install
```

3. Set up environment variables

```bash
cp .env.example .env.local
```

4. Run the development server

```bash
pnpm dev
```

## 📚 Documentation

For detailed documentation, setup guides, and implementation details, see the [`/docs`](./docs) directory:

- **[📖 Documentation Index](./docs/README.md)** - Complete documentation overview
- **[🚀 Quick Start Guide](./docs/QUICK_START.md)** - Get up and running quickly
- **[🚢 Deployment Guide](./docs/DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[📊 Implementation Progress](./docs/IMPLEMENTATION_PROGRESS.md)** - Current development status
- **[🔌 API Implementation](./docs/API_IMPLEMENTATION.md)** - Backend API documentation
- **[💳 Stripe Setup](./docs/STRIPE_SETUP.md)** - Payment processing configuration

## ✨ Features

- **AI-Powered Upscaling**: Enhance images to 2x, 4x, and 8x resolution
- **Batch Processing**: Process multiple images simultaneously (Pro/Explorer plans)
- **Subscription Management**: Flexible pricing with Stripe integration
- **Credit System**: Fair usage tracking with daily/monthly allocations
- **Real-time Processing**: Monitor progress with live updates
- **Multi-language Support**: English and Chinese localization
- **Responsive Design**: Works on desktop and mobile devices

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Cloudflare Workers, Supabase
- **Authentication**: NextAuth.js with Google OAuth
- **Payments**: Stripe subscriptions and webhooks
- **Database**: PostgreSQL with Supabase
- **UI Components**: shadcn/ui, Radix UI
- **Deployment**: Vercel (frontend), Cloudflare Workers (backend)

## 🎨 Customization

- **Theme**: Customize colors in `app/theme.css` using [shadcn-ui-theme-generator](https://zippystarter.com/tools/shadcn-ui-theme-generator)
- **Content**: Update landing page content in `i18n/pages/landing`
- **Translations**: Modify messages in `i18n/messages`
- **Pricing**: Configure plans in `models/subscription.ts`

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy automatically

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new)

### Cloudflare Workers (Backend)

1. Set up environment variables:

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

2. Configure variables in `wrangler.toml`

3. Deploy:

```bash
npm run cf:deploy
```

For detailed deployment instructions, see [Deployment Guide](./docs/DEPLOYMENT_GUIDE.md).

## 📊 Project Status

- ✅ **Phase 1**: Foundation (Database, Task Management, Subscriptions)
- ✅ **Phase 2**: Core Features (UI, Authentication, API Integration)
- 🚧 **Phase 3**: Automation & Analytics (In Progress)

See [Implementation Progress](./docs/IMPLEMENTATION_PROGRESS.md) for detailed status.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add documentation to `/docs` if needed
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

Built with ❤️ using modern web technologies
