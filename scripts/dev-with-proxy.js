#!/usr/bin/env node

/**
 * 启动带代理的开发服务器
 * 借鉴 toylivewall 的代理启动方式
 */

// 启用代理环境变量
process.env.ENABLE_PROXY = 'true';
process.env.PROXY_URL = process.env.PROXY_URL || 'http://127.0.0.1:7890';

// 加载全局代理配置
require('../lib/proxy-setup');

console.log('🚀 [Dev with Proxy] 启动带代理的开发服务器...');
console.log('🌐 [Dev with Proxy] 代理地址:', process.env.PROXY_URL);

// 启动 Next.js 开发服务器
const { spawn } = require('child_process');

const child = spawn('pnpm', ['dev'], {
  stdio: 'inherit',
  env: process.env,
  cwd: process.cwd()
});

child.on('exit', (code) => {
  console.log(`🏁 [Dev with Proxy] 开发服务器已退出，退出码: ${code}`);
  process.exit(code);
});

child.on('error', (error) => {
  console.error('❌ [Dev with Proxy] 启动失败:', error);
  process.exit(1);
});