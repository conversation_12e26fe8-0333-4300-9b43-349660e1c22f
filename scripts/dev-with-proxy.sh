#!/bin/bash

# 设置代理环境变量
export PROXY_ENABLED=true
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
export HTTP_PROXY=http://127.0.0.1:7890
export ALL_PROXY=socks5://127.0.0.1:7890

# 显示代理设置
echo "🌐 代理设置已启用:"
echo "  https_proxy: $https_proxy"
echo "  http_proxy: $http_proxy"
echo "  all_proxy: $all_proxy"
echo ""

# 启动开发服务器
echo "🚀 启动开发服务器..."
NODE_NO_WARNINGS=1 pnpm dev
