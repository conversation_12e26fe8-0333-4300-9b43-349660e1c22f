import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";

// Import next-intl middleware with error handling
let intlMiddleware: any = null;
try {
  const createMiddleware = require("next-intl/middleware").default;
  const { routing } = require("./i18n/routing");
  intlMiddleware = createMiddleware(routing);
} catch (error) {
  console.warn('next-intl middleware not available:', error);
}

export default async function middleware(request: NextRequest) {
  // Apply internationalization middleware first (if available)
  let response = NextResponse.next();
  
  if (intlMiddleware) {
    try {
      response = intlMiddleware(request);
    } catch (error) {
      console.warn('Intl middleware error:', error);
      response = NextResponse.next();
    }
  }

  // Check for subscription-protected routes
  const protectedRoutes = ['/batch-upscale', '/subscription'];
  const pathname = request.nextUrl.pathname;

  // Skip middleware for API routes and static files
  if (pathname.startsWith('/api/') || pathname.startsWith('/_next/') || pathname.includes('.')) {
    return response;
  }

  // Check if current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.includes(route)
  );

  if (isProtectedRoute) {
    try {
      const session = await auth();

      if (!session || !session.user) {
        // Redirect to sign in if not authenticated
        const signInUrl = new URL('/auth/signin', request.url);
        signInUrl.searchParams.set('callbackUrl', pathname);
        return NextResponse.redirect(signInUrl);
      }

      // Check subscription requirements for batch processing
      if (pathname.includes('/batch-upscale')) {
        const canUseBatch = session.user?.can_use_batch;

        if (!canUseBatch) {
          // Redirect to pricing page if user doesn't have batch access
          const pricingUrl = new URL('/pricing', request.url);
          pricingUrl.searchParams.set('upgrade', 'batch');
          return NextResponse.redirect(pricingUrl);
        }
      }
    } catch (error) {
      console.error('Middleware auth check failed:', error);
      // On error, allow the request to continue
      return response;
    }
  }

  return response;
}

export const config = {
  matcher: [
    "/",
    "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|ja|ko|ru|fr|de|ar|es|it)/:path*",
    "/((?!privacy-policy|terms-of-service|api/|_next|_vercel|.*\\..*).*)",
  ],
  runtime: 'nodejs',
};
