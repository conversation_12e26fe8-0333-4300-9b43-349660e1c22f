export interface Task {
  id: string;
  user_id: string;
  task_type: 'single' | 'batch';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  scale: number;
  total_images: number;
  processed_images: number;
  external_task_ids: string[];
  input_urls: string[];
  output_urls: string[];
  credits_cost: number;
  processing_time_seconds?: number;
  error_message?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface CreateTaskRequest {
  task_type: 'single' | 'batch';
  input_urls: string[];
  scale: number;
  credits_cost: number;
  metadata?: Record<string, any>;
}

export interface TaskProgress {
  task_id: string;
  status: Task['status'];
  processed_images: number;
  total_images: number;
  current_image?: string;
  progress_percentage: number;
}

export interface TaskResult {
  task_id: string;
  output_urls: string[];
  processing_time_seconds: number;
  credits_used: number;
}