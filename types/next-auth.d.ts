import "next-auth";
import { Subscription, SubscriptionPlan } from "./subscription";

declare module "next-auth" {
  interface JWT {
    user?: {
      uuid?: string;
      id?: string; // Modern UUID primary key
      nickname?: string;
      avatar_url?: string;
      created_at?: string;
      subscription?: Subscription | null;
      plan?: SubscriptionPlan;
      credits_balance?: number;
      can_use_batch?: boolean;
      subscription_tier?: string;
      daily_usage_count?: number;
      daily_usage_reset_at?: string;
    };
  }

  interface Session {
    user: {
      uuid?: string;
      id?: string; // Modern UUID primary key
      nickname?: string;
      avatar_url?: string;
      created_at?: string;
      subscription?: Subscription | null;
      plan?: SubscriptionPlan;
      credits_balance?: number;
      can_use_batch?: boolean;
      subscription_tier?: string;
      daily_usage_count?: number;
      daily_usage_reset_at?: string;
    } & DefaultSession["user"];
  }
}
