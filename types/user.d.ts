export interface User {
  // Modern schema fields
  id: string; // UUID
  email: string;
  name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  
  // Authentication details
  provider: string;
  provider_id: string;
  
  // Subscription details
  subscription_status: 'free' | 'pro' | 'explorer';
  subscription_tier: 'free' | 'pro' | 'explorer';
  credits_balance: number;
  
  // Usage tracking
  daily_usage_count: number;
  daily_usage_reset_at: string;
  total_usage_count: number;
  
  // User preferences
  locale: string;
  timezone: string;
  last_login_at?: string;
  last_login_ip?: string;
  
  // Legacy compatibility fields (for migration)
  uuid?: string;
  nickname?: string;
  signin_type?: string;
  signin_provider?: string;
  signin_openid?: string;
  signin_ip?: string;
  
  // Status
  status: 'active' | 'suspended' | 'deleted';
}

export interface UserSession {
  uuid: string;
  email: string;
  nickname?: string;
  avatar_url?: string;
  created_at: string;
}

// Legacy interface for compatibility
export interface UserCredits {
  one_time_credits?: number;
  monthly_credits?: number;
  total_credits?: number;
  used_credits?: number;
  left_credits: number;
  free_credits?: number;
  is_recharged?: boolean;
  is_pro?: boolean;
}
