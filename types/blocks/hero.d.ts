import { Button, Image, Announcement } from "@/types/blocks/base";

// 新增功能组件类型
export type FeatureComponent = 
  | { type: "upscale-images"; props: UpscaleImagesProps }
  // 可以继续扩展其他功能组件类型
  // | { type: "other-feature"; props: OtherFeatureProps }
  ;

// 新增图片放大组件配置
export interface UpscaleImagesProps {
  title?: string;
  description?: string;
  maxScale?: number;
  allowedFormats?: string[];
  showPreview?: boolean;
  uploadLabel?: string;
}

export interface Announcement {
  title?: string;
  description?: string;
  label?: string;
  url?: string;
  target?: string;
}

export interface Hero {
  name?: string;
  disabled?: boolean;
  announcement?: Announcement;
  title?: string;
  highlight_text?: string;
  description?: string;
  buttons?: Button[];
  image?: Image;
  tip?: string;
  show_happy_users?: boolean;
  show_badge?: boolean;
  
  // 新增功能组件配置区域
  feature?: FeatureComponent;
}
