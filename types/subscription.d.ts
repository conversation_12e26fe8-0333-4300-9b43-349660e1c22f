export interface Subscription {
  id: string;
  user_id: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  stripe_price_id?: string;
  plan_id: 'free' | 'pro' | 'explorer';
  plan_name: string;
  credits_included: number;
  price_amount: number; // in cents
  price_currency: string;
  billing_interval: 'month' | 'year';
  status: 'active' | 'canceled' | 'past_due' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  canceled_at?: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionPlan {
  id: 'free' | 'pro' | 'explorer';
  name: string;
  description: string;
  features: string[];
  pricing: {
    monthly?: {
      amount: number;
      currency: string;
      stripe_price_id: string;
    };
    yearly?: {
      amount: number;
      currency: string;
      stripe_price_id: string;
      discount_percentage: number;
    };
  };
  credits: {
    daily?: number;
    monthly?: number;
  };
  limits: {
    batch_processing: boolean;
    max_batch_size?: number;
    priority_queue: boolean;
  };
}

export interface UserSubscriptionInfo {
  subscription?: Subscription;
  plan: SubscriptionPlan;
  credits_balance: number;
  daily_usage_count: number;
  daily_usage_reset_at: string;
  can_use_batch: boolean;
}