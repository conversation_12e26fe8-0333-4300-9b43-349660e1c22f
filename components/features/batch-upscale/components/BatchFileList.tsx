"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Trash2, 
  X, 
  Image as ImageIcon,
  Play,
  RotateCcw
} from "lucide-react";
import { BatchUploadFile, BatchProcessingStatus } from "../types";

interface BatchFileListProps {
  files: BatchUploadFile[];
  onRemove: (fileId: string) => void;
  onClearAll: () => void;
  scale: number;
  onScaleChange: (scale: number) => void;
  onStartProcessing: () => void;
  processingStatus: BatchProcessingStatus;
}

export function BatchFileList({
  files,
  onRemove,
  onClearAll,
  scale,
  onScaleChange,
  onStartProcessing,
  processingStatus
}: BatchFileListProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusInfo = (status: BatchUploadFile['status']) => {
    switch (status) {
      case 'pending':
        return {
          badge: <Badge className="bg-gray-100 text-gray-700 border-gray-300">Pending</Badge>,
          icon: '⏳',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
      case 'processing':
        return {
          badge: <Badge className="bg-blue-100 text-blue-700 border-blue-300">Processing</Badge>,
          icon: '⚡',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'completed':
        return {
          badge: <Badge className="bg-green-100 text-green-700 border-green-300">Completed</Badge>,
          icon: '✅',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'error':
        return {
          badge: <Badge className="bg-red-100 text-red-700 border-red-300">Error</Badge>,
          icon: '❌',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200'
        };
      default:
        return {
          badge: null,
          icon: '❓',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  return (
    <Card className="border-gray-200 shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center">
              <ImageIcon className="w-4 h-4" />
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">Selected Images</div>
              <div className="text-sm text-gray-500">{files.length} files ready for processing</div>
            </div>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onClearAll}
              disabled={processingStatus === 'processing'}
              className="text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Clear All
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enhanced Processing Options */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <div className="flex-1">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Enhancement Settings</h4>
              
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <label className="text-sm font-medium text-gray-700">Scale Factor:</label>
                  <Select
                    value={scale.toString()}
                    onValueChange={(value) => onScaleChange(parseInt(value))}
                    disabled={processingStatus === 'processing'}
                  >
                    <SelectTrigger className="w-24 bg-white border-gray-300">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2">2x</SelectItem>
                      <SelectItem value="4">4x</SelectItem>
                      <SelectItem value="8">8x</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="text-sm text-gray-600">
                  Output resolution: {scale}x larger
                </div>
              </div>
            </div>
            
            <Button
              onClick={onStartProcessing}
              disabled={files.length === 0 || processingStatus === 'processing'}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 text-sm font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
            >
              {processingStatus === 'processing' ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Start Batch Processing
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Enhanced File List */}
        <div className="space-y-3">
          {files.map((file) => {
            const statusInfo = getStatusInfo(file.status);
            return (
              <div
                key={file.id}
                className={`flex items-center gap-4 p-4 border-2 rounded-xl transition-all duration-200 hover:shadow-md ${statusInfo.bgColor} ${statusInfo.borderColor}`}
              >
                {/* Enhanced Preview */}
                <div className="relative w-16 h-16 rounded-xl overflow-hidden bg-white shadow-sm flex-shrink-0">
                  <img
                    src={file.preview}
                    alt={file.name}
                    className="w-full h-full object-cover"
                  />
                  {/* Status Icon Overlay */}
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white shadow-sm flex items-center justify-center text-xs">
                    {statusInfo.icon}
                  </div>
                </div>

                {/* Enhanced File Info */}
                <div className="flex-1 min-w-0">
                  <p className="font-semibold text-gray-900 truncate mb-1">{file.name}</p>
                  <div className="flex items-center gap-3 text-sm text-gray-600">
                    <span>{formatFileSize(file.size)}</span>
                    <span>•</span>
                    <span className="capitalize">{file.status}</span>
                  </div>
                  
                  {/* Progress Bar for Processing Files */}
                  {file.status === 'processing' && (
                    <div className="mt-2">
                      <div className="flex items-center justify-between text-xs text-blue-600 mb-1">
                        <span>Processing...</span>
                        <span className="font-semibold">{file.progress.toFixed(0)}%</span>
                      </div>
                      <div className="h-2 bg-blue-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-blue-500 transition-all duration-300 rounded-full"
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Status Badge */}
                <div className="flex items-center gap-3">
                  {statusInfo.badge}
                  
                  {/* Remove Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemove(file.id)}
                    disabled={processingStatus === 'processing'}
                    className="text-gray-400 hover:text-red-500 hover:bg-red-50 h-8 w-8 rounded-lg"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>

        {files.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <ImageIcon className="w-8 h-8 text-gray-400" />
            </div>
            <h4 className="text-lg font-semibold text-gray-700 mb-1">No files selected</h4>
            <p className="text-gray-500">Upload images above to start batch processing</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
