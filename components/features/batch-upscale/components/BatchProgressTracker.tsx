"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Loader2,
  Image as ImageIcon
} from "lucide-react";
import { BatchUploadFile, BatchProcessingStatus, BatchStatusSummary } from "../types";

interface BatchProgressTrackerProps {
  files: BatchUploadFile[];
  processingStatus: BatchProcessingStatus;
  statusSummary: BatchStatusSummary;
}

export function BatchProgressTracker({
  files,
  processingStatus,
  statusSummary
}: BatchProgressTrackerProps) {
  const overallProgress = statusSummary.total > 0 
    ? (statusSummary.completed / statusSummary.total) * 100 
    : 0;

  const getStatusIcon = (status: BatchUploadFile['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-muted-foreground" />;
      case 'processing':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: BatchUploadFile['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-muted';
      case 'processing':
        return 'bg-blue-500';
      case 'completed':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-muted';
    }
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Overall Progress */}
      <Card className="border-gray-200 shadow-sm bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-xl flex items-center justify-center">
              <ImageIcon className="w-6 h-6" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 mb-1">
                AI Enhancement in Progress
              </CardTitle>
              <p className="text-gray-600">
                Processing {statusSummary.total} images with advanced algorithms
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enhanced Progress Bar */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-900">Overall Progress</span>
              <span className="text-lg font-bold text-blue-600">
                {statusSummary.completed} / {statusSummary.total} completed
              </span>
            </div>
            
            <div className="relative">
              <div className="h-4 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 rounded-full relative"
                  style={{ width: `${overallProgress}%` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse" />
                </div>
              </div>
              <div className="absolute -top-1 left-0 text-xs font-bold text-blue-600">
                0%
              </div>
              <div className="absolute -top-1 right-0 text-xs font-bold text-blue-600">
                100%
              </div>
            </div>
            
            <div className="text-center">
              <span className="text-2xl font-bold text-blue-600">
                {Math.round(overallProgress)}%
              </span>
              <span className="text-gray-600 ml-2">complete</span>
            </div>
          </div>

          {/* Enhanced Status Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-white rounded-xl p-4 text-center border border-gray-200 shadow-sm">
              <div className="w-8 h-8 bg-gray-100 text-gray-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Clock className="w-4 h-4" />
              </div>
              <div className="text-2xl font-bold text-gray-600">{statusSummary.pending}</div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
            <div className="bg-white rounded-xl p-4 text-center border border-blue-200 shadow-sm">
              <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Loader2 className="w-4 h-4 animate-spin" />
              </div>
              <div className="text-2xl font-bold text-blue-600">{statusSummary.processing}</div>
              <div className="text-sm text-blue-500">Processing</div>
            </div>
            <div className="bg-white rounded-xl p-4 text-center border border-green-200 shadow-sm">
              <div className="w-8 h-8 bg-green-100 text-green-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                <CheckCircle className="w-4 h-4" />
              </div>
              <div className="text-2xl font-bold text-green-600">{statusSummary.completed}</div>
              <div className="text-sm text-green-500">Completed</div>
            </div>
            <div className="bg-white rounded-xl p-4 text-center border border-red-200 shadow-sm">
              <div className="w-8 h-8 bg-red-100 text-red-600 rounded-lg flex items-center justify-center mx-auto mb-2">
                <XCircle className="w-4 h-4" />
              </div>
              <div className="text-2xl font-bold text-red-600">{statusSummary.error}</div>
              <div className="text-sm text-red-500">Failed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Individual File Progress */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-100 text-gray-600 rounded-lg flex items-center justify-center">
              <ImageIcon className="w-4 h-4" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Processing Queue</CardTitle>
              <p className="text-sm text-gray-600">Real-time progress for each image</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {files.map((file, index) => (
                <div
                  key={file.id}
                  className={`flex items-center gap-4 p-4 rounded-xl border-2 transition-all duration-200 ${
                    file.status === 'processing' ? 'bg-blue-50 border-blue-200 shadow-md' :
                    file.status === 'completed' ? 'bg-green-50 border-green-200' :
                    file.status === 'error' ? 'bg-red-50 border-red-200' :
                    'bg-gray-50 border-gray-200'
                  }`}
                >
                  {/* Enhanced Preview */}
                  <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-white shadow-sm flex-shrink-0">
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="w-full h-full object-cover"
                    />
                    {/* Position indicator */}
                    <div className="absolute -top-1 -left-1 w-5 h-5 bg-gray-800 text-white text-xs rounded-full flex items-center justify-center font-bold">
                      {index + 1}
                    </div>
                  </div>

                  {/* Enhanced File Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <p className="font-semibold text-gray-900 truncate">{file.name}</p>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(file.status)}
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                          file.status === 'processing' ? 'bg-blue-100 text-blue-700' :
                          file.status === 'completed' ? 'bg-green-100 text-green-700' :
                          file.status === 'error' ? 'bg-red-100 text-red-700' :
                          'bg-gray-100 text-gray-700'
                        }`}>
                          {file.status.charAt(0).toUpperCase() + file.status.slice(1)}
                        </span>
                      </div>
                    </div>
                    
                    {file.status === 'processing' && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-blue-600 font-medium">Processing...</span>
                          <span className="text-blue-600 font-bold">{file.progress.toFixed(0)}%</span>
                        </div>
                        <div className="h-2 bg-blue-200 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-blue-500 transition-all duration-300 rounded-full"
                            style={{ width: `${file.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {file.status === 'completed' && (
                      <div className="text-xs text-green-600 font-medium">
                        ✓ Enhancement complete
                      </div>
                    )}
                    
                    {file.status === 'error' && file.error && (
                      <div className="text-xs text-red-600 font-medium">
                        ❌ {file.error}
                      </div>
                    )}
                    
                    {file.status === 'pending' && (
                      <div className="text-xs text-gray-500">
                        Waiting in queue...
                      </div>
                    )}
                  </div>

                  {/* Enhanced Status Indicator */}
                  <div className="flex-shrink-0">
                    <div className={`w-4 h-4 rounded-full border-2 border-white shadow-sm ${getStatusColor(file.status)}`} />
                  </div>
                </div>
            ))}
          </div>

          {files.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-gray-400" />
              </div>
              <h4 className="text-lg font-semibold text-gray-700 mb-1">No files to process</h4>
              <p className="text-gray-500">Go to the Upload tab to add images for batch processing</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Processing Status */}
      {processingStatus !== 'idle' && (
        <Card className={`border-2 shadow-lg ${
          processingStatus === 'processing' ? 'border-blue-300 bg-blue-50' :
          processingStatus === 'completed' ? 'border-green-300 bg-green-50' :
          'border-red-300 bg-red-50'
        }`}>
          <CardContent className="py-8">
            <div className="flex flex-col items-center justify-center gap-4">
              {processingStatus === 'processing' && (
                <>
                  <div className="relative">
                    <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center">
                      <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                    </div>
                    <div className="absolute -inset-2 bg-blue-500 rounded-2xl opacity-20 animate-ping"></div>
                  </div>
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-blue-800 mb-1">AI Enhancement in Progress</h3>
                    <p className="text-blue-600">Our advanced algorithms are enhancing your images...</p>
                  </div>
                </>
              )}
              {processingStatus === 'completed' && (
                <>
                  <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mb-2">
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-green-800 mb-1">Batch Processing Complete! 🎉</h3>
                    <p className="text-green-600">All images have been successfully enhanced and are ready for download</p>
                  </div>
                </>
              )}
              {processingStatus === 'error' && (
                <>
                  <div className="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center mb-2">
                    <XCircle className="w-8 h-8 text-red-600" />
                  </div>
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-red-800 mb-1">Processing Failed</h3>
                    <p className="text-red-600">Some images could not be processed. Please try again or contact support.</p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
