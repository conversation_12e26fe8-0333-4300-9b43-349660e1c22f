"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Download, 
  Eye, 
  RotateCcw,
  Image as ImageIcon,
  ExternalLink,
  Archive
} from "lucide-react";
import { BatchUploadFile } from "../types";
import { toast } from "sonner";

interface BatchResultsGalleryProps {
  files: BatchUploadFile[];
  onStartNew: () => void;
}

export function BatchResultsGallery({
  files,
  onStartNew
}: BatchResultsGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<BatchUploadFile | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadImage = async (file: BatchUploadFile) => {
    if (!file.resultUrl) return;

    try {
      const response = await fetch(file.resultUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `upscaled_${file.name}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success(`Downloaded ${file.name}`);
    } catch (error) {
      toast.error('Failed to download image');
      console.error('Download error:', error);
    }
  };

  const downloadAll = async () => {
    setIsDownloading(true);
    try {
      // Import JSZip dynamically to avoid SSR issues
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();
      
      // Add each processed image to the ZIP
      for (const file of files) {
        if (file.resultUrl) {
          try {
            const response = await fetch(file.resultUrl);
            const blob = await response.blob();
            const fileName = `upscaled_${file.name}`;
            zip.file(fileName, blob);
            toast.success(`Added ${file.name} to ZIP`, { duration: 1000 });
          } catch (error) {
            console.error(`Failed to add ${file.name} to ZIP:`, error);
            toast.error(`Failed to add ${file.name} to ZIP`);
          }
        }
      }
      
      // Generate and download the ZIP file
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const url = window.URL.createObjectURL(zipBlob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `batch_upscaled_images_${Date.now()}.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success(`Downloaded ZIP with ${files.length} upscaled images!`);
    } catch (error) {
      console.error('Bulk download error:', error);
      toast.error('Failed to create ZIP file. Falling back to individual downloads.');
      
      // Fallback to individual downloads
      for (const file of files) {
        if (file.resultUrl) {
          await downloadImage(file);
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
    } finally {
      setIsDownloading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (files.length === 0) {
    return (
      <Card className="border-gray-200 shadow-sm">
        <CardContent className="text-center py-16">
          <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <ImageIcon className="w-10 h-10 text-blue-500" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-2">No results yet</h3>
          <p className="text-gray-600 text-lg mb-8 max-w-md mx-auto">
            Complete batch processing to see your enhanced images here. Results will appear as they're processed.
          </p>
          <Button 
            onClick={onStartNew}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
          >
            <RotateCcw className="w-5 h-5 mr-2" />
            Start New Batch
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Results Header */}
      <Card className="border-gray-200 shadow-sm bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardHeader className="pb-6">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-green-100 text-green-600 rounded-xl flex items-center justify-center">
                <ImageIcon className="w-6 h-6" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-gray-900 mb-1">
                  Batch Processing Complete! 
                </CardTitle>
                <p className="text-gray-600">
                  {files.length} images successfully enhanced with AI upscaling
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700 font-medium">All files processed</span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                onClick={downloadAll}
                disabled={isDownloading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 active:scale-95"
              >
                {isDownloading ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Creating ZIP...
                  </>
                ) : (
                  <>
                    <Archive className="w-5 h-5 mr-2" />
                    Download All as ZIP
                  </>
                )}
              </Button>
              <Button 
                onClick={onStartNew}
                variant="outline"
                className="border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3"
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Start New Batch
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Results Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {files.map((file) => (
          <Card key={file.id} className="overflow-hidden border-gray-200 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] group">
            <div className="aspect-square relative">
              {/* Enhanced Before/After Images */}
              <div className="grid grid-cols-2 h-full">
                {/* Original */}
                <div className="relative overflow-hidden">
                  <img
                    src={file.preview}
                    alt={`Original ${file.name}`}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute top-3 left-3">
                    <Badge className="bg-red-100 text-red-700 border-red-300 text-xs font-semibold">
                      Before
                    </Badge>
                  </div>
                </div>
                
                {/* Upscaled */}
                <div className="relative overflow-hidden">
                  <img
                    src={file.resultUrl}
                    alt={`Upscaled ${file.name}`}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute top-3 right-3">
                    <Badge className="bg-green-100 text-green-700 border-green-300 text-xs font-semibold">
                      After
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Enhanced Overlay Actions */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-4">
                <div className="flex items-center gap-3">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setSelectedImage(file)}
                    className="bg-white/90 hover:bg-white text-gray-900 shadow-lg"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Preview
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => downloadImage(file)}
                    className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>

              {/* Quality Enhancement Indicator */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-1 h-8 bg-white/80 rounded-full shadow-lg"></div>
              </div>
            </div>

            <CardContent className="p-4 bg-gradient-to-r from-gray-50 to-white">
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 truncate">{file.name}</h3>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">{formatFileSize(file.size)}</span>
                  <Badge className="bg-green-100 text-green-700 border-green-300 text-xs">
                    ✓ Enhanced
                  </Badge>
                </div>
                
                {/* Progress indicator for visual feedback */}
                <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-r from-green-500 to-blue-500 rounded-full"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Image Preview Dialog */}
      <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ImageIcon className="w-5 h-5" />
              {selectedImage?.name}
            </DialogTitle>
          </DialogHeader>
          
          {selectedImage && (
            <div className="space-y-4">
              {/* Before/After Comparison */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Original</h4>
                  <div className="aspect-square rounded-lg overflow-hidden border">
                    <img
                      src={selectedImage.preview}
                      alt="Original"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Upscaled</h4>
                  <div className="aspect-square rounded-lg overflow-hidden border">
                    <img
                      src={selectedImage.resultUrl}
                      alt="Upscaled"
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2 pt-4 border-t">
                <Button
                  onClick={() => downloadImage(selectedImage)}
                  className="flex-1"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Upscaled
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.open(selectedImage.resultUrl, '_blank')}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open in New Tab
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
