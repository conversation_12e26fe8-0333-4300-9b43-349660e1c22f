"use client";

import { useState, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Upload, 
  Image as ImageIcon, 
  CheckCircle, 
  XCircle, 
  Clock,
  Download,
  Trash2,
  AlertCircle
} from "lucide-react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { BatchUploadFile, BatchProcessingStatus } from "./types";
import { BatchFileList } from "./components/BatchFileList";
import { BatchProgressTracker } from "./components/BatchProgressTracker";
import { BatchResultsGallery } from "./components/BatchResultsGallery";
import { useUserState } from "../upscale-images/hooks/useUserState";
import { taskService } from "@/services/task";

export function BatchUpscale() {
  const [files, setFiles] = useState<BatchUploadFile[]>([]);
  const [processingStatus, setProcessingStatus] = useState<BatchProcessingStatus>('idle');
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [scale, setScale] = useState(2);
  const [activeTab, setActiveTab] = useState("upload");
  
  const { isPro, isExplorer, maxBatchSize } = useUserState();

  const canUseBatch = isPro || isExplorer;

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (!canUseBatch) {
      toast.error("Batch processing requires Pro or Explorer subscription");
      return;
    }

    if (acceptedFiles.length > maxBatchSize) {
      toast.error(`Maximum ${maxBatchSize} files allowed for your plan`);
      return;
    }

    const newFiles: BatchUploadFile[] = acceptedFiles.map((file, index) => ({
      id: `${Date.now()}-${index}`,
      file,
      name: file.name,
      size: file.size,
      preview: URL.createObjectURL(file),
      status: 'pending',
      progress: 0
    }));

    setFiles(prev => [...prev, ...newFiles]);
    toast.success(`Added ${acceptedFiles.length} files to batch`);
  }, [canUseBatch, maxBatchSize]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.webp']
    },
    maxFiles: maxBatchSize,
    disabled: processingStatus === 'processing'
  });

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearAll = () => {
    setFiles([]);
    setProcessingStatus('idle');
    setCurrentTaskId(null);
  };

  const startBatchProcessing = async () => {
    if (files.length === 0) {
      toast.error("No files to process");
      return;
    }

    if (!canUseBatch) {
      toast.error("Batch processing requires Pro or Explorer subscription");
      return;
    }

    setProcessingStatus('processing');
    setActiveTab('progress');

    try {
      // Prepare input URLs
      const inputUrls = files.map(f => f.preview);
      
      // Create batch task
      const task = await taskService.createUpscaleTask(
        'current-user', // This should come from auth context
        inputUrls,
        scale,
        'batch'
      );

      setCurrentTaskId(task.id);

      // Monitor batch progress
      taskService.monitorTask(task.id, {
        onProgress: (progress) => {
          // Update individual file progress based on batch progress
          const completedCount = Math.floor((progress.progress_percentage / 100) * files.length);
          setFiles(prev => prev.map((file, index) => ({
            ...file,
            status: index < completedCount ? 'completed' : 
                   index === completedCount ? 'processing' : 'pending',
            progress: index < completedCount ? 100 : 
                     index === completedCount ? (progress.progress_percentage % (100 / files.length)) * files.length : 0
          })));
        },
        onComplete: (result) => {
          setProcessingStatus('completed');
          setFiles(prev => prev.map((file, index) => ({
            ...file,
            status: 'completed',
            progress: 100,
            resultUrl: result.urls?.[index] || ""
          })));
          setActiveTab('results');
          toast.success("Batch processing completed!");
        },
        onError: (error) => {
          setProcessingStatus('error');
          setFiles(prev => prev.map(file => ({
            ...file,
            status: file.status === 'processing' ? 'error' : file.status
          })));
          toast.error(`Batch processing failed: ${error}`);
        }
      });

    } catch (error) {
      setProcessingStatus('error');
      toast.error("Failed to start batch processing");
      console.error('Batch processing error:', error);
    }
  };

  const getStatusSummary = () => {
    const pending = files.filter(f => f.status === 'pending').length;
    const processing = files.filter(f => f.status === 'processing').length;
    const completed = files.filter(f => f.status === 'completed').length;
    const error = files.filter(f => f.status === 'error').length;
    
    return { pending, processing, completed, error, total: files.length };
  };

  const statusSummary = getStatusSummary();

  if (!canUseBatch) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
            <Upload className="w-8 h-8 text-muted-foreground" />
          </div>
          <CardTitle>Batch Processing</CardTitle>
          <CardDescription>
            Process multiple images at once with our batch upscaling feature
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <AlertCircle className="w-5 h-5 text-amber-600 mx-auto mb-2" />
            <p className="text-amber-800 font-medium">Pro or Explorer subscription required</p>
            <p className="text-amber-700 text-sm mt-1">
              Upgrade your plan to process multiple images simultaneously
            </p>
          </div>
          <Button onClick={() => window.location.href = '/subscription'}>
            Upgrade to Pro
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-4 md:p-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">Batch Image Upscaling</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Process up to {maxBatchSize} images simultaneously with advanced AI enhancement. 
          Perfect for bulk photo editing workflows.
        </p>
        <div className="flex items-center justify-center gap-2 text-sm text-blue-600 font-medium">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span>{isPro ? 'Pro' : 'Explorer'} Plan Active</span>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-gray-100 p-1 rounded-xl h-auto">
          <TabsTrigger 
            value="upload" 
            className="flex items-center gap-2 py-3 px-4 rounded-lg text-sm font-medium transition-all data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-gray-900 text-gray-600"
          >
            <Upload className="w-4 h-4" />
            <span className="hidden sm:inline">Upload Images</span>
            <span className="sm:hidden">Upload</span>
            {files.length > 0 && (
              <span className="bg-blue-100 text-blue-700 text-xs px-2 py-0.5 rounded-full font-semibold">
                {files.length}
              </span>
            )}
          </TabsTrigger>
          <TabsTrigger 
            value="progress" 
            className="flex items-center gap-2 py-3 px-4 rounded-lg text-sm font-medium transition-all data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-gray-900 text-gray-600"
          >
            <Clock className="w-4 h-4" />
            <span className="hidden sm:inline">Processing</span>
            <span className="sm:hidden">Progress</span>
            {processingStatus === 'processing' && (
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            )}
          </TabsTrigger>
          <TabsTrigger 
            value="results" 
            className="flex items-center gap-2 py-3 px-4 rounded-lg text-sm font-medium transition-all data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-gray-900 text-gray-600"
          >
            <ImageIcon className="w-4 h-4" />
            <span className="hidden sm:inline">Results</span>
            <span className="sm:hidden">Results</span>
            {statusSummary.completed > 0 && (
              <span className="bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full font-semibold">
                {statusSummary.completed}
              </span>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-8">
          {/* Enhanced Upload Area */}
          <Card className={`
            relative overflow-hidden transition-all duration-300 ease-out cursor-pointer border-2
            ${isDragActive 
              ? 'border-blue-500 bg-blue-50 shadow-lg scale-[1.01]' 
              : 'border-dashed border-gray-300 hover:border-blue-400 hover:bg-gray-50/80'
            }
            ${processingStatus === 'processing' ? 'opacity-50 cursor-not-allowed' : ''}
          `}>
            <CardContent className="p-8 md:p-12">
              <div {...getRootProps()}>
                <input {...getInputProps()} />
                
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600"></div>
                </div>
                
                <div className="relative text-center">
                  {/* Upload Icon */}
                  <div className={`
                    w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center transition-all duration-300
                    ${isDragActive 
                      ? 'bg-blue-100 text-blue-600 scale-110' 
                      : 'bg-gray-100 text-gray-500 hover:bg-blue-50 hover:text-blue-500 hover:scale-105'
                    }
                  `}>
                    <Upload className="w-10 h-10" />
                  </div>

                  {/* Content */}
                  {isDragActive ? (
                    <div className="animate-in fade-in-50 slide-in-from-bottom-2 duration-200">
                      <h3 className="text-2xl font-bold text-blue-700 mb-2">Drop your images here</h3>
                      <p className="text-blue-600 text-lg">Release to upload {files.length > 0 ? 'additional ' : ''}files</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <h3 className="text-2xl font-bold text-gray-800">
                        Drag & drop images here, or click to select
                      </h3>
                      <p className="text-gray-600 text-lg">
                        PNG, JPG, WEBP • Up to {maxBatchSize} files • Max 10MB each
                      </p>
                      
                      {/* Features */}
                      <div className="flex flex-wrap justify-center gap-6 pt-4">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span>Batch Processing</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>AI Enhancement</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span>High Quality</span>
                        </div>
                      </div>
                      
                      <div className="pt-4">
                        <span className="inline-flex items-center px-6 py-3 bg-blue-50 text-blue-700 text-lg font-semibold rounded-xl border-2 border-blue-200 hover:bg-blue-100 transition-colors">
                          <Upload className="w-5 h-5 mr-2" />
                          Choose Files
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Animated Border */}
                <div className={`
                  absolute inset-0 rounded-lg transition-opacity duration-300
                  ${isDragActive ? 'opacity-100' : 'opacity-0'}
                `}>
                  <div className="absolute inset-0 rounded-lg border-2 border-blue-500 animate-pulse"></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* File List */}
          {files.length > 0 && (
            <BatchFileList 
              files={files}
              onRemove={removeFile}
              onClearAll={clearAll}
              scale={scale}
              onScaleChange={setScale}
              onStartProcessing={startBatchProcessing}
              processingStatus={processingStatus}
            />
          )}
        </TabsContent>

        <TabsContent value="progress">
          <BatchProgressTracker 
            files={files}
            processingStatus={processingStatus}
            statusSummary={statusSummary}
          />
        </TabsContent>

        <TabsContent value="results">
          <BatchResultsGallery 
            files={files.filter(f => f.status === 'completed')}
            onStartNew={() => {
              clearAll();
              setActiveTab('upload');
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
