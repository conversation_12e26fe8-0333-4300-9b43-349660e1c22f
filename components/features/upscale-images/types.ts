export interface UploadFile {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'done' | 'error';
}

export interface UploadImagesProps {
  onUpload?: (files: File[]) => void;
  maxFiles?: number;
  accept?: Record<string, string[]>;
}

export interface UpscaleImagesProps {
  title?: string;
  description?: string;
  maxScale?: number;
  allowedFormats?: string[];
  showPreview?: boolean;
}

export type ProcessingState = 'IDLE' | 'PROCESSING' | 'FINISHED' | 'ERROR';

export interface ImageData {
  original: string;
  processed?: string;
} 