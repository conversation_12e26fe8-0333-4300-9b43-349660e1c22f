import { useState, useEffect } from "react";

interface UpscaleOptionsProps {
  onScale: (scale: number) => void;
  disabled: boolean;
  onReset: () => void;
  currentScale?: number;
}

export const UpscaleOptions = ({
  onScale,
  disabled,
  onReset,
  currentScale = 2
}: UpscaleOptionsProps) => {
  const [selectedScale, setSelectedScale] = useState<string>(currentScale.toString());
  const scales = [
    { value: "2", label: "2x", description: "Good for small images" },
    { value: "4", label: "4x", description: "Recommended" },
    { value: "8", label: "8x", description: "High quality" },
    { value: "16", label: "16x", description: "Maximum detail" }
  ];

  useEffect(() => {
    setSelectedScale(currentScale.toString());
  }, [currentScale]);

  useEffect(() => {
    return () => {
      onReset();
    };
  }, [onReset]);

  return (
    <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-1">Enhancement Level</h3>
        <p className="text-sm text-gray-600">Choose the upscaling factor for your image</p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
        {scales.map((scale) => (
          <button
            key={scale.value}
            onClick={() => setSelectedScale(scale.value)}
            disabled={disabled}
            className={`relative group flex flex-col items-center justify-center p-4 rounded-lg transition-all duration-200 border-2
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              ${selectedScale === scale.value 
                ? 'border-blue-500 bg-blue-50 text-blue-700' 
                : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50/50 text-gray-700'
              }`}
          >
            <div className="text-xl font-bold mb-1">
              {scale.label}
            </div>
            <div className="text-xs text-center leading-tight">
              {scale.description}
            </div>
            
            {/* Selection indicator */}
            {selectedScale === scale.value && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </button>
        ))}
      </div>

      <button 
        className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 group
          ${disabled 
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]'
          }`}
        disabled={disabled}
        onClick={() => onScale(Number(selectedScale))}
      >
        {disabled ? (
          <div className="flex items-center justify-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>Processing...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span>Enhance Image ({selectedScale}x)</span>
            <svg 
              className="w-4 h-4 transition-transform group-hover:translate-x-1" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M14 5l7 7m0 0l-7 7m7-7H3" 
              />
            </svg>
          </div>
        )}
      </button>
    </div>
  );
}; 