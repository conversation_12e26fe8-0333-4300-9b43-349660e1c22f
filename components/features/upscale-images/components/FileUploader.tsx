import { useDropzone } from "react-dropzone";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { UploadFile } from "../types";
import { useUserState } from "../hooks/useUserState";
import { upscaleService } from "@/services/upscale";
import { useEffect, useRef } from "react";

interface FileUploaderProps {
  onUpload: (files: UploadFile[]) => void;
  scale?: number;
  onProcessed?: (originalUrl: string, processedUrl: string) => void;
  files?: UploadFile[]; // 添加已上传的文件列表
}

export const FileUploader = ({ 
  onUpload, 
  scale = 2,
  onProcessed,
  files = []
}: FileUploaderProps) => {
  const { isPro, maxImages } = useUserState();
  const cleanupFunctions = useRef<(() => void)[]>([]);
  const previousScale = useRef<number>(scale);
  
  // 清理所有任务监控
  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(cleanup => cleanup());
      cleanupFunctions.current = [];
    };
  }, []);
  
  // 当放大倍数变化时，重新处理已上传的图片
  useEffect(() => {
    const handleScaleChange = async () => {
      // 只有当放大倍数变化且有已上传的图片时才重新处理
      if (scale !== previousScale.current && files.length > 0) {
        previousScale.current = scale;
        
        try {
          for (const file of files) {
            if (file.status === 'done') {
              // 创建新的放大任务
              const taskResponse = await upscaleService.createUpscaleTask(file.preview, scale);
              
              // 监控任务状态
              if (taskResponse.taskId) {
                const cleanup = upscaleService.monitorTaskStatus(
                  taskResponse.taskId,
                  {
                    onProgress: (progress) => {
                      console.log(`Task ${taskResponse.taskId} progress: ${progress}%`);
                    },
                    onComplete: (result) => {
                      console.log(`Task ${taskResponse.taskId} completed:`, result);
                      if (result.url && onProcessed) {
                        onProcessed(file.preview, result.url);
                      }
                      toast.success("Image processing completed");
                    },
                    onError: (error) => {
                      console.error(`Task ${taskResponse.taskId} error:`, error);
                      toast.error(`Processing failed: ${error}`);
                    }
                  }
                );
                
                // 保存清理函数
                cleanupFunctions.current.push(cleanup);
              }
            }
          }
        } catch (error) {
          toast.error("Failed to process image");
        }
      }
    };
    
    handleScaleChange();
  }, [scale, files, onProcessed]);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.webp']
    },
    maxFiles: isPro ? 5 : 1,
    onDrop: (acceptedFiles) => {
      if (!isPro && acceptedFiles.length > 1) {
        toast.error("Multiple upload requires Pro subscription");
        return;
      }

      const newFiles = acceptedFiles.map(file => ({
        id: Math.random().toString(36).substring(7),
        file,
        preview: URL.createObjectURL(file),
        status: 'pending' as const
      }));

      onUpload(newFiles);
    }
  });

  return (
    <Card className={`
      relative overflow-hidden transition-all duration-300 ease-out cursor-pointer group
      ${isDragActive 
        ? 'border-blue-500 bg-blue-50 border-2 shadow-lg scale-[1.02]' 
        : 'border-dashed border-2 border-gray-300 hover:border-blue-400 hover:bg-gray-50/80'
      }
    `}>
      <div {...getRootProps()} className="relative text-center p-8 md:p-12">
        <input {...getInputProps()} />
        
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600"></div>
        </div>
        
        <div className="relative flex flex-col items-center gap-4">
          {/* Upload Icon */}
          <div className={`
            w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-300
            ${isDragActive 
              ? 'bg-blue-100 text-blue-600 scale-110' 
              : 'bg-gray-100 text-gray-500 group-hover:bg-blue-50 group-hover:text-blue-500 group-hover:scale-105'
            }
          `}>
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>

          {/* Text Content */}
          {isDragActive ? (
            <div className="animate-in fade-in-50 slide-in-from-bottom-2 duration-200">
              <h3 className="text-xl font-semibold text-blue-700 mb-1">Drop your images here</h3>
              <p className="text-blue-600">Release to upload</p>
            </div>
          ) : (
            <div className="space-y-2">
              <h3 className="text-xl font-semibold text-gray-800 group-hover:text-gray-900">
                Drop image here or click to upload
              </h3>
              <p className="text-gray-600 group-hover:text-gray-700">
                PNG, JPG or WEBP • {isPro ? "Up to 5 images" : "Max 1 image"}
              </p>
              <div className="pt-2">
                <span className="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 text-sm font-medium rounded-full border border-blue-200">
                  <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Choose files
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Animated Border */}
        <div className={`
          absolute inset-0 rounded-lg transition-opacity duration-300
          ${isDragActive ? 'opacity-100' : 'opacity-0'}
        `}>
          <div className="absolute inset-0 rounded-lg border-2 border-blue-500 animate-pulse"></div>
        </div>
      </div>
    </Card>
  );
}; 