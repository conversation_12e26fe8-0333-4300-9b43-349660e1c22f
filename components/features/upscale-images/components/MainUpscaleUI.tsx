"use client";

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface MainUpscaleUIProps {
  onImageUpload?: (file: File) => void;
}

export const MainUpscaleUI = ({ onImageUpload }: MainUpscaleUIProps) => {
  const [position, setPosition] = useState(50);
  const [isDragActive, setIsDragActive] = useState(false);
  const [zoom, setZoom] = useState(1);
  const containerRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Sample images for the bottom carousel
  const sampleImages = [
    "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", 
    "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face"
  ];

  const originalImage = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=560&h=560&fit=crop&crop=face";
  const enhancedImage = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=560&h=560&fit=crop&crop=face&auto=enhance";

  const handleMove = (event: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging.current || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const x = 'touches' in event 
      ? event.touches[0].clientX 
      : event.clientX;
    const position = ((x - containerRect.left) / containerRect.width) * 100;
    
    setPosition(Math.min(Math.max(0, position), 100));
  };

  const handleMouseDown = () => {
    isDragging.current = true;
  };

  const handleMouseUp = () => {
    isDragging.current = false;
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find(file => file.type.startsWith('image/'));
    
    if (imageFile && onImageUpload) {
      onImageUpload(imageFile);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && onImageUpload) {
      onImageUpload(file);
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  useEffect(() => {
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('touchend', handleMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchend', handleMouseUp);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation */}
      <nav className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded"></div>
            <span className="text-xl font-semibold text-gray-900">AI Image Upscaler</span>
          </div>
          <div className="flex items-center space-x-4">
            <button className="p-2 text-gray-500 hover:text-gray-700">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>
            <button className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium">
              Sign In
            </button>
          </div>
        </div>
      </nav>

      <div className="flex min-h-[calc(100vh-80px)]">
        {/* Left side - Image comparison */}
        <div className="flex-1 p-6 flex items-center justify-center">
          <div className="relative w-full max-w-lg">
            {/* Image labels */}
            <div className="absolute -top-10 left-4 z-20 bg-black/75 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
              Original WEBP: 560 × 560 p...
            </div>
            <div className="absolute -top-10 right-4 z-20 bg-black/75 text-white px-3 py-1.5 rounded-lg text-sm font-medium">
              AI Face Enhancement WEBP...
            </div>

            {/* Main image container */}
            <div 
              ref={containerRef}
              className="relative w-full aspect-square rounded-2xl overflow-hidden shadow-xl bg-white"
              onMouseMove={handleMove}
              onTouchMove={handleMove}
              style={{ transform: `scale(${zoom})` }}
            >
            {/* Original image */}
            <div
              className="absolute inset-0"
              style={{ clipPath: `inset(0 ${100 - position}% 0 0)` }}
            >
              <img
                src={originalImage}
                alt="Original"
                className="w-full h-full object-cover"
                draggable={false}
              />
            </div>

            {/* Enhanced image */}
            <img
              src={enhancedImage}
              alt="Enhanced"
              className="absolute inset-0 w-full h-full object-cover"
              draggable={false}
            />

            {/* Slider */}
            <div
              className="absolute top-0 bottom-0 z-10 group cursor-ew-resize"
              style={{ left: `${position}%` }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleMouseDown}
            >
              {/* Divider line */}
              <div className="absolute inset-y-0 -left-0.5 w-1 bg-white shadow-lg" />
              
              {/* Slider handle */}
              <div className="absolute top-1/2 -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-white rounded-full shadow-xl border-2 border-gray-200 flex items-center justify-center transition-all duration-200 group-hover:scale-110">
                <div className="flex items-center gap-0.5">
                  <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M15 19l-7-7 7-7" />
                  </svg>
                  <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Expand icon */}
            <button className="absolute top-4 right-4 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg flex items-center justify-center hover:bg-white transition-colors">
              <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
            </button>
          </div>

            {/* Zoom controls */}
            <div className="flex items-center justify-center mt-6 space-x-3">
              <button 
                onClick={() => setZoom(Math.max(0.5, zoom - 0.1))}
                className="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              
              <div className="flex-1 h-2 bg-gray-200 rounded-full max-w-24">
                <div 
                  className="h-full bg-gray-600 rounded-full transition-all duration-200"
                  style={{ width: `${(zoom - 0.5) / 1.5 * 100}%` }}
                />
              </div>
              
              <button 
                onClick={() => setZoom(Math.min(2, zoom + 0.1))}
                className="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
              
              <span className="text-sm text-gray-600 font-medium">Zoom</span>
              
              <button className="w-8 h-8 bg-white rounded-full shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors">
                <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M12 6v6l4 2" stroke="white" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Right side - Upload area */}
        <div className="w-[480px] bg-white border-l border-gray-200 p-8 flex flex-col">
          {/* Header */}
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Image Upscaler Online for free via AI
            </h1>
            <p className="text-gray-600 text-sm leading-relaxed">
              It's free! Upscale and enlarge images & photos with 1-click. Enhance and download in seconds.
            </p>
          </div>

          {/* Upload area */}
          <div 
            className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 mb-6 ${
              isDragActive 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Button 
              onClick={triggerFileSelect}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-xl mb-3 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Upload Image
            </Button>
            
            <p className="text-gray-500 text-sm mb-3">
              Drag & drop to upload your images
            </p>
            
            <button className="text-blue-600 hover:text-blue-700 font-medium flex items-center justify-center gap-2 mx-auto text-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Upload multiple files &gt;
            </button>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              className="hidden"
              onChange={handleFileSelect}
            />
          </div>

          {/* Sample images */}
          <div className="mt-auto">
            <div className="flex items-center justify-between mb-3">
              <div>
                <p className="font-semibold text-gray-900 text-sm">No image on hand?</p>
                <p className="text-gray-600 text-sm">Try one of these</p>
              </div>
              <button className="text-gray-400 hover:text-gray-600">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
            
            <div className="flex gap-2 overflow-x-auto pb-2">
              {sampleImages.map((src, index) => (
                <button
                  key={index}
                  className="flex-shrink-0 w-14 h-14 rounded-lg overflow-hidden border-2 border-gray-200 hover:border-blue-400 transition-colors"
                  onClick={() => {
                    // Handle sample image selection
                  }}
                >
                  <img 
                    src={src} 
                    alt={`Sample ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};