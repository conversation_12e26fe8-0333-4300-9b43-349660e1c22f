import { Button } from "@/components/ui/button";

interface ProcessingOverlayProps {
  progress: number;
  onCancel?: () => void;
}

export const ProcessingOverlay = ({ progress, onCancel }: ProcessingOverlayProps) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded-lg z-10">
      <div className="w-full max-w-xs p-4 space-y-4">
        <div className="space-y-3">
          <div className="relative h-1.5 w-full overflow-hidden rounded-full">
            <div className="absolute inset-0 bg-gradient-to-r from-muted/50 to-muted/30" />
            <div 
              className="absolute inset-y-0 left-0 bg-gradient-to-r from-cyan-500 to-purple-500 transition-all duration-300"
              style={{ width: `${progress}%` }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/25 to-transparent animate-shimmer" 
                style={{ 
                  backgroundSize: '200% 100%',
                  animationDuration: '2s'
                }}
              />
            </div>
          </div>
          <p className="text-sm text-center text-muted-foreground">
            Processing... {Math.round(progress)}%
          </p>
        </div>

        {onCancel && (
          <Button
            variant="ghost"
            size="sm"
            className="w-full text-sm"
            onClick={onCancel}
          >
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
}; 