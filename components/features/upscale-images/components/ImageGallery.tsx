import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { ImageCompare } from "./ImageCompare";
import { ImageData } from "../types";

interface ImageGalleryProps {
  images: ImageData[];
  onRemove: (index: number) => void;
  isProcessing?: boolean;
  progress?: number;
}

export const ImageGallery = ({
  images,
  onRemove,
  isProcessing,
  progress = 0
}: ImageGalleryProps) => {
  const handleDownload = (url: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = `upscaled-${Date.now()}.png`;
    document.body.appendChild(link);
    link.click();
    link.remove();
  };

  return (
    <div className="rounded-xl bg-gradient-to-b from-muted/50 to-muted/10 p-4 md:p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">Processing Results</h3>
        {images.length > 0 && (
          <Button 
            variant="ghost" 
            size="icon"
            className="h-8 w-8 rounded-full hover:bg-background/80 hover:shadow-sm transition-all"
            onClick={() => onRemove(0)}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="space-y-6">
        {images.map((image, index) => (
          <div key={index} className="relative">
            <ImageCompare
              original={image.original}
              processed={image.processed}
              onDownload={() => handleDownload(image.processed!)}
              isProcessing={isProcessing && index === images.length - 1}
              progress={progress}
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 h-6 w-6 rounded-full bg-background/80 backdrop-blur-sm shadow-sm hover:bg-background"
              onClick={() => onRemove(index)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}; 