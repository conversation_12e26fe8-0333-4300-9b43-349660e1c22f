"use client";

import { useState, useRef } from "react";
import { toast } from "sonner";
import { FileUploader } from "./components/FileUploader";
import { UploadPreview } from "./components/UploadPreview";
import { UpscaleOptions } from "./components/UpscaleOptions";
import { ProcessingOverlay } from "./components/ProcessingOverlay";
import { ImageGallery } from "./components/ImageGallery";
import { UploadFile, UpscaleImagesProps, ProcessingState, ImageData } from "./types";
import { upscaleService } from "@/services/upscale";

interface UploadImagesProps {
  onUpload?: (files: File[]) => void;
  maxFiles?: number;
  accept?: Record<string, string[]>;
}

// Mock user state
const useUserState = () => {
  return {
    isPro: false,
    maxImages: 1,
  };
};

export const UpscaleImages = ({
  title,
  description,
  maxScale = 16,
  allowedFormats = ["image/*"],
  showPreview = true
}: UpscaleImagesProps) => {
  const [uploads, setUploads] = useState<UploadFile[]>([]);
  const [processingState, setProcessingState] = useState<ProcessingState>('IDLE');
  const [processedImages, setProcessedImages] = useState<ImageData[]>([]);
  const [progress, setProgress] = useState(0);
  const [currentScale, setCurrentScale] = useState(2);
  const processingRef = useRef<NodeJS.Timeout>();

  const handleUpload = async (files: UploadFile[]) => {
    setUploads(files);
    setProcessingState('PROCESSING');
    setProgress(0);

    try {
      for (const file of files) {
        const imageUrl = await upscaleService.uploadImage(file.file);
        const taskResponse = await upscaleService.createUpscaleTask(imageUrl, currentScale);
        
        file.status = 'done';
        file.preview = imageUrl;
        setUploads([...files]);
        
        if (taskResponse.taskId) {
          upscaleService.monitorTaskStatus(
            taskResponse.taskId,
            {
              onProgress: (progress: number) => {
                setProgress(progress);
              },
              onComplete: (result: { url: string }) => {
                if (result.url) {
                  handleProcessed(imageUrl, result.url);
                }
                toast.success("Image processing completed");
              },
              onError: (error: string) => {
                toast.error(`Processing failed: ${error}`);
                setProcessingState('ERROR');
              }
            }
          );
        }
      }
    } catch (error) {
      toast.error("Failed to process image");
      files.forEach(file => {
        file.status = 'error';
      });
      setUploads([...files]);
      setProcessingState('ERROR');
    }
  };

  const handleRemoveUpload = (id: string) => {
    setUploads(prev => {
      const newUploads = prev.filter(upload => upload.id !== id);
      if (newUploads.length === 0) {
        setProcessingState('IDLE');
      }
      return newUploads;
    });
  };

  const handleScale = async (scale: number) => {
    if (scale === currentScale) {
      return;
    }

    setCurrentScale(scale);
    setProcessingState('PROCESSING');
    setProgress(0);

    try {
      const lastProcessedImage = processedImages[processedImages.length - 1];
      if (lastProcessedImage) {
        const taskResponse = await upscaleService.createUpscaleTask(lastProcessedImage.original, scale);
        
        if (taskResponse.taskId) {
          upscaleService.monitorTaskStatus(
            taskResponse.taskId,
            {
              onProgress: (progress: number) => {
                setProgress(progress);
              },
              onComplete: (result: { url: string }) => {
                if (result.url) {
                  setProcessedImages(prev => {
                    const newImages = [...prev];
                    newImages[newImages.length - 1] = {
                      original: lastProcessedImage.original,
                      processed: result.url
                    };
                    return newImages;
                  });
                }
                setProcessingState('FINISHED');
                toast.success("Image processing completed");
              },
              onError: (error: string) => {
                toast.error(`Processing failed: ${error}`);
                setProcessingState('ERROR');
              }
            }
          );
        }
      }
    } catch (error) {
      toast.error("Failed to process image");
      setProcessingState('ERROR');
    }
  };

  const handleProcessed = (originalUrl: string, processedUrl: string) => {
    setProcessedImages(prev => [
      ...prev,
      {
        original: originalUrl,
        processed: processedUrl
      }
    ]);
    setProcessingState('FINISHED');
    setProgress(100);
  };

  const handleCancelProcessing = () => {
    if (processingRef.current) {
      clearTimeout(processingRef.current);
    }
    setProcessingState('IDLE');
    setProgress(0);
  };

  const handleOptionsReset = () => {
    setProcessingState('IDLE');
    setProgress(0);
  };

  const handleClear = (index: number) => {
    setProcessedImages(prev => prev.filter((_, i) => i !== index));
  };

  const allUploadsComplete = uploads.every(upload => upload.status === 'done');

  return (
    <div className="max-w-4xl mx-auto p-4 md:p-8 space-y-8">
      <div className="text-center space-y-4">
        {title && <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">{title}</h1>}
        {description && <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">{description}</p>}
      </div>
      
      <FileUploader 
        onUpload={handleUpload} 
        scale={currentScale}
        onProcessed={handleProcessed}
      />

      {uploads.length > 0 && (
        <div className="space-y-3">
          {uploads.map(upload => (
            <UploadPreview
              key={upload.id}
              upload={upload}
              onRemove={handleRemoveUpload}
            />
          ))}
        </div>
      )}

      {allUploadsComplete && uploads.length > 0 && (
        <div className="relative">
          <UpscaleOptions
            onScale={handleScale}
            disabled={processingState === 'PROCESSING'}
            onReset={handleOptionsReset}
            currentScale={currentScale}
          />
          {processingState === 'PROCESSING' && (
            <ProcessingOverlay 
              progress={progress}
              onCancel={handleCancelProcessing}
            />
          )}
        </div>
      )}

      {processedImages.length > 0 && (
        <ImageGallery 
          images={processedImages} 
          onRemove={handleClear}
          isProcessing={processingState === 'PROCESSING'}
          progress={progress}
        />
      )}
    </div>
  );
};
