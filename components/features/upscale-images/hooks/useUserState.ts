import { useSession } from "next-auth/react";

export interface UserState {
  isPro: boolean;
  isExplorer: boolean;
  maxImages: number;
  maxBatchSize: number;
  canUseBatch: boolean;
  isLoading: boolean;
}

export const useUserState = (): UserState => {
  const { data: session, status } = useSession();

  const isLoading = status === "loading";
  const plan = session?.user?.plan;
  const isPro = plan?.id === 'pro';
  const isExplorer = plan?.id === 'explorer';
  const canUseBatch = session?.user?.can_use_batch || false;

  return {
    isPro,
    isExplorer,
    maxImages: isPro || isExplorer ? 5 : 1,
    maxBatchSize: isExplorer ? 100 : isPro ? 50 : 1,
    canUseBatch,
    isLoading,
  };
};