import { useTranslations } from "next-intl";

export function StructuredData() {
  const t = useTranslations();

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "ImageUpscaler",
    "description": t("metadata.description"),
    "url": "https://imageupscaler.run",
    "applicationCategory": "MultimediaApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Free AI Image Upscaling Service"
    },
    "creator": {
      "@type": "Organization",
      "name": "ImageUpscaler Team",
      "url": "https://imageupscaler.run"
    },
    "publisher": {
      "@type": "Organization",
      "name": "ImageUpscaler",
      "url": "https://imageupscaler.run"
    },
    "featureList": [
      "AI-powered image upscaling",
      "4K and 8K enhancement",
      "Preserve natural details",
      "Free online tool",
      "Multiple image formats support",
      "Batch processing"
    ],
    "screenshot": "https://imageupscaler.run/screenshot.png",
    "softwareVersion": "1.0",
    "datePublished": "2025-01-01",
    "dateModified": new Date().toISOString().split('T')[0],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2)
      }}
    />
  );
}

export function OrganizationStructuredData() {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ImageUpscaler",
    "url": "https://imageupscaler.run",
    "logo": "https://imageupscaler.run/logo.png",
    "description": "AI-powered image upscaling service that enhances photos to 4K/8K quality",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": "https://imageupscaler.run/contact"
    },
    "sameAs": [
      "https://twitter.com/imageupscaler",
      "https://github.com/imageupscaler"
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationData, null, 2)
      }}
    />
  );
} 