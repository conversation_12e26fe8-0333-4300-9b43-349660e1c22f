"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  CreditCard, 
  Calendar, 
  TrendingUp, 
  Settings, 
  Download,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { UserSubscriptionInfo } from "@/types/subscription";
import { UserCredits } from "@/types/user";
import { PricingV2 } from "@/components/blocks/pricing-v2";
import { formatDistanceToNow } from "date-fns";

interface SubscriptionDashboardProps {
  subscriptionInfo: UserSubscriptionInfo;
  userCredits: UserCredits | null;
}

export function SubscriptionDashboard({ 
  subscriptionInfo, 
  userCredits 
}: SubscriptionDashboardProps) {
  const [activeTab, setActiveTab] = useState("overview");
  const [isUpgrading, setIsUpgrading] = useState(false);

  const { subscription, plan, credits_balance, daily_usage_count, daily_usage_reset_at } = subscriptionInfo;

  // Calculate usage percentage
  const getUsagePercentage = () => {
    if (plan.id === 'free') {
      return (daily_usage_count / (plan.credits.daily || 5)) * 100;
    }
    if (plan.credits.monthly) {
      return (credits_balance / plan.credits.monthly) * 100;
    }
    return 0;
  };

  const handlePlanSelect = async (planId: string, interval: 'monthly' | 'yearly') => {
    setIsUpgrading(true);
    try {
      const { SUBSCRIPTION_PLANS } = await import("@/models/subscription");
      const selectedPlan = SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS];

      if (!selectedPlan || planId === 'free') {
        return;
      }

      const pricing = selectedPlan.pricing[interval];
      if (!pricing) {
        throw new Error('Pricing not available for selected interval');
      }

      // Create checkout session
      const response = await fetch('/api/subscription/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          price_id: pricing.stripe_price_id,
          success_url: `${window.location.origin}/subscription?success=true`,
          cancel_url: `${window.location.origin}/subscription?canceled=true`,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create checkout session');
      }

      const { checkout_url } = await response.json();

      // Redirect to Stripe checkout
      window.location.href = checkout_url;
    } catch (error) {
      console.error('Upgrade failed:', error);
      // You might want to show a toast error here
    } finally {
      setIsUpgrading(false);
    }
  };

  const getStatusBadge = () => {
    if (!subscription) {
      return <Badge variant="secondary">Free</Badge>;
    }
    
    switch (subscription.status) {
      case 'active':
        return <Badge variant="default">Active</Badge>;
      case 'canceled':
        return <Badge variant="destructive">Canceled</Badge>;
      case 'past_due':
        return <Badge variant="destructive">Past Due</Badge>;
      default:
        return <Badge variant="secondary">{subscription.status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="upgrade">Upgrade</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Current Plan Card */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Current Plan
                  </CardTitle>
                  <CardDescription>
                    Your current subscription details
                  </CardDescription>
                </div>
                {getStatusBadge()}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h3 className="font-semibold text-lg">{plan.name}</h3>
                  <p className="text-sm text-muted-foreground">{plan.description}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Credits</p>
                  <p className="font-semibold">
                    {plan.id === 'free' 
                      ? `${plan.credits.daily} daily credits`
                      : `${plan.credits.monthly} monthly credits`
                    }
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">
                    {subscription ? 'Next billing' : 'Resets'}
                  </p>
                  <p className="font-semibold">
                    {subscription 
                      ? formatDistanceToNow(new Date(subscription.current_period_end), { addSuffix: true })
                      : formatDistanceToNow(new Date(daily_usage_reset_at), { addSuffix: true })
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Credits Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Credits Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {plan.id === 'free' ? 'Daily Usage' : 'Monthly Usage'}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    {plan.id === 'free' 
                      ? `${daily_usage_count} / ${plan.credits.daily}`
                      : `${credits_balance} remaining`
                    }
                  </span>
                </div>
                <Progress value={getUsagePercentage()} className="w-full" />
                {plan.id === 'free' && daily_usage_count >= (plan.credits.daily || 5) && (
                  <div className="flex items-center gap-2 text-amber-600">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">
                      Daily limit reached. Resets {formatDistanceToNow(new Date(daily_usage_reset_at), { addSuffix: true })}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Usage Analytics</CardTitle>
              <CardDescription>
                Track your credit usage and processing history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Usage analytics coming soon</p>
                <p className="text-sm">We're working on detailed usage reports and analytics</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>
                View your payment history and download invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              {subscription ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Download className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Billing history coming soon</p>
                  <p className="text-sm">Integration with Stripe billing portal in progress</p>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No billing history</p>
                  <p className="text-sm">You're currently on the free plan</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upgrade" className="space-y-6">
          <PricingV2 
            onSelectPlan={handlePlanSelect}
            currentPlan={plan.id}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
