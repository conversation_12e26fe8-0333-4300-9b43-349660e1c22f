"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Star } from "lucide-react";
import { SUBSCRIPTION_PLANS } from "@/models/subscription";

interface PricingV2Props {
  onSelectPlan?: (planId: string, interval: 'monthly' | 'yearly') => void;
  currentPlan?: string;
}

export function PricingV2({ onSelectPlan, currentPlan }: PricingV2Props) {
  // Default to yearly billing as requested
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('yearly');

  const plans = [
    SUBSCRIPTION_PLANS.free,
    SUBSCRIPTION_PLANS.pro,
    SUBSCRIPTION_PLANS.explorer
  ];

  const calculateSavings = (planId: 'pro' | 'explorer') => {
    const plan = SUBSCRIPTION_PLANS[planId];
    if (!plan.pricing.monthly || !plan.pricing.yearly) return 0;
    
    const monthlyYearly = plan.pricing.monthly.amount * 12;
    const yearly = plan.pricing.yearly.amount;
    return ((monthlyYearly - yearly) / 100).toFixed(2);
  };

  const formatPrice = (amount: number) => {
    return `$${(amount / 100).toFixed(2)}`;
  };

  return (
    <div className="py-12">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4">Choose Your Plan</h2>
        <p className="text-muted-foreground mb-6">
          Upgrade anytime. Cancel anytime. Start with a free account.
        </p>
        
        {/* Billing Toggle */}
        <div className="inline-flex items-center rounded-full bg-muted p-1">
          <button
            onClick={() => setBillingInterval('monthly')}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              billingInterval === 'monthly'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setBillingInterval('yearly')}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors relative ${
              billingInterval === 'yearly'
                ? 'bg-background text-foreground shadow-sm'
                : 'text-muted-foreground'
            }`}
          >
            Yearly
            <Badge variant="secondary" className="ml-2 text-xs">
              Save 17%
            </Badge>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
        {plans.map((plan) => {
          const isCurrentPlan = currentPlan === plan.id;
          const isPopular = plan.id === 'pro';
          const isFree = plan.id === 'free';
          
          // Get pricing for current interval
          const pricing = isFree ? null : plan.pricing[billingInterval];
          const monthlyPricing = isFree ? null : plan.pricing.monthly;
          
          return (
            <Card
              key={plan.id}
              className={`relative p-6 ${
                isPopular ? 'border-primary shadow-lg' : ''
              } ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
            >
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground">
                    <Star className="w-3 h-3 mr-1" />
                    Most Popular
                  </Badge>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  {plan.description}
                </p>

                {isFree ? (
                  <div className="mb-4">
                    <div className="text-3xl font-bold">Free</div>
                    <div className="text-sm text-muted-foreground">
                      {plan.credits.daily} daily credits
                    </div>
                  </div>
                ) : (
                  <div className="mb-4">
                    <div className="text-3xl font-bold">
                      {formatPrice(pricing!.amount)}
                      <span className="text-lg font-normal text-muted-foreground">
                        /{billingInterval === 'yearly' ? 'year' : 'month'}
                      </span>
                    </div>
                    
                    {billingInterval === 'yearly' && monthlyPricing && (
                      <div className="text-sm text-muted-foreground">
                        <span className="line-through">
                          {formatPrice(monthlyPricing.amount * 12)}/year
                        </span>
                        <span className="text-green-600 ml-2 font-medium">
                          Save ${calculateSavings(plan.id as 'pro' | 'explorer')}
                        </span>
                      </div>
                    )}
                    
                    <div className="text-sm text-muted-foreground mt-1">
                      {plan.credits.monthly} monthly credits
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <Check className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>

              <Button
                onClick={() => onSelectPlan?.(plan.id, billingInterval)}
                className="w-full"
                variant={isCurrentPlan ? "outline" : isPopular ? "default" : "outline"}
                disabled={isCurrentPlan}
              >
                {isCurrentPlan
                  ? "Current Plan"
                  : isFree
                  ? "Get Started"
                  : `Choose ${plan.name}`}
              </Button>

              {!isFree && billingInterval === 'yearly' && (
                <div className="text-center mt-3">
                  <Badge variant="outline" className="text-xs">
                    🎉 {plan.pricing.yearly?.discount_percentage}% discount applied
                  </Badge>
                </div>
              )}
            </Card>
          );
        })}
      </div>

      <div className="text-center mt-8 text-sm text-muted-foreground">
        <p>
          All plans include our core features. Upgrade or downgrade at any time.
          <br />
          Free users get 5 daily credits that reset at UTC midnight.
        </p>
      </div>
    </div>
  );
}