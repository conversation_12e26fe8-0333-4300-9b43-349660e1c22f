import { getSupabaseClient } from "@/models/db";
import { getSession } from "next-auth/react";

/**
 * Get the modern UUID from session (either id or uuid field)
 */
export function getModernUserIdFromSession(session: any): string | null {
  if (!session?.user) return null;
  
  // Prefer the modern id field, fallback to legacy uuid
  return session.user.id || session.user.uuid || null;
}

/**
 * Convert legacy UUID to modern UUID if needed
 */
export async function convertToModernUserId(userIdOrUuid: string): Promise<string | null> {
  const supabase = getSupabaseClient();
  
  // First try to find by id (modern UUID)
  const { data: userById } = await supabase
    .from('users')
    .select('id')
    .eq('id', userIdOrUuid)
    .single();
  
  if (userById) {
    return userById.id;
  }
  
  // If not found, try to find by legacy uuid
  const { data: userByUuid } = await supabase
    .from('users')
    .select('id')
    .eq('uuid', userIdOrUuid)
    .single();
  
  if (userByUuid) {
    return userByUuid.id;
  }
  
  return null;
}

/**
 * Get the current user's modern UUID from session
 */
export async function getCurrentUserModernId(): Promise<string | null> {
  const session = await getSession();
  const userIdFromSession = getModernUserIdFromSession(session);
  
  if (!userIdFromSession) {
    return null;
  }
  
  return await convertToModernUserId(userIdFromSession);
}

/**
 * Get user info with subscription details
 */
export async function getCurrentUserInfo() {
  const session = await getSession();
  
  if (!session?.user) {
    return null;
  }
  
  const modernUserId = await getCurrentUserModernId();
  
  return {
    ...session.user,
    modernId: modernUserId,
    isAuthenticated: true,
    subscription_tier: session.user.subscription_tier || 'free',
    credits_balance: session.user.credits_balance || 0,
    can_use_batch: session.user.can_use_batch || false
  };
}