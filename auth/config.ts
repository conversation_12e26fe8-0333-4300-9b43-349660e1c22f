import CredentialsProvider from "next-auth/providers/credentials";
import GitHub<PERSON>rovider from "next-auth/providers/github";
import GoogleProvider from "next-auth/providers/google";
import { NextAuthConfig } from "next-auth";
import { Provider } from "next-auth/providers/index";
import { getClientIp } from "@/lib/ip";
import { getIsoTimestr } from "@/lib/time";
import { getUuid } from "@/lib/hash";
import { saveUser } from "@/services/user";
// 简化的代理配置：由于使用全局代理，NextAuth 会自动使用代理
// 不需要复杂的条件导入和代理配置
console.log('🔐 [Auth Config] 使用全局代理配置，所有请求将自动使用代理（如果启用）');

let providers: Provider[] = [];

// Google One Tap Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED === "true" &&
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID
) {
  providers.push(
    CredentialsProvider({
      id: "google-one-tap",
      name: "google-one-tap",

      credentials: {
        credential: { type: "text" },
      },

      async authorize(credentials, req) {
        const googleClientId = process.env.NEXT_PUBLIC_AUTH_GOOGLE_ID;
        if (!googleClientId) {
          console.log("invalid google auth config");
          return null;
        }

        const token = credentials!.credential;
        
        console.log('🔐 [Google One Tap] 验证 Google token（自动使用全局代理）');

        const response = await fetch(
          "https://oauth2.googleapis.com/tokeninfo?id_token=" + token
        );
        if (!response.ok) {
          console.log("Failed to verify token");
          return null;
        }

        const payload = await response.json();
        if (!payload) {
          console.log("invalid payload from token");
          return null;
        }

        const {
          email,
          sub,
          given_name,
          family_name,
          email_verified,
          picture: image,
        } = payload;
        if (!email) {
          console.log("invalid email in payload");
          return null;
        }

        const user = {
          id: sub,
          name: [given_name, family_name].join(" "),
          email,
          image,
          emailVerified: email_verified ? new Date() : null,
        };

        return user;
      },
    })
  );
}

// Google Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GOOGLE_ENABLED === "true" &&
  process.env.AUTH_GOOGLE_ID &&
  process.env.AUTH_GOOGLE_SECRET
) {
  providers.push(
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    })
  );
}

// Github Auth
if (
  process.env.NEXT_PUBLIC_AUTH_GITHUB_ENABLED === "true" &&
  process.env.AUTH_GITHUB_ID &&
  process.env.AUTH_GITHUB_SECRET
) {
  providers.push(
    GitHubProvider({
      clientId: process.env.AUTH_GITHUB_ID,
      clientSecret: process.env.AUTH_GITHUB_SECRET,
    })
  );
}

export const providerMap = providers
  .map((provider) => {
    if (typeof provider === "function") {
      const providerData = provider();
      return { id: providerData.id, name: providerData.name };
    } else {
      return { id: provider.id, name: provider.name };
    }
  })
  .filter((provider) => provider.id !== "google-one-tap");

console.log('🔐 [NextAuth Config] 使用全局代理配置，NextAuth 请求将自动使用代理');

export const authOptions: NextAuthConfig = {
  providers,
  pages: {
    signIn: "/auth/signin",
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      const isAllowedToSignIn = true;
      if (isAllowedToSignIn) {
        return true;
      } else {
        // Return false to display a default error message
        return false;
        // Or you can return a URL to redirect to:
        // return '/unauthorized'
      }
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
    async session({ session, token, user }) {
      if (token && token.user && token.user) {
        session.user = token.user;
      }
      return session;
    },
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      try {
        if (user && user.email && account) {
          const userUuid = getUuid();
          const currentTime = getIsoTimestr();
          
          const dbUser = {
            // Modern schema fields
            id: userUuid, // Use UUID as primary key
            email: user.email,
            name: user.name || "",
            avatar_url: user.image || "",
            created_at: currentTime,
            updated_at: currentTime,
            
            // Authentication details
            provider: account.provider,
            provider_id: account.providerAccountId,
            
            // Subscription details
            subscription_status: 'free' as const,
            subscription_tier: 'free' as const,
            credits_balance: 5,
            
            // Usage tracking
            daily_usage_count: 0,
            daily_usage_reset_at: currentTime,
            total_usage_count: 0,
            
            // User preferences
            locale: 'en',
            timezone: 'UTC',
            
            // Legacy compatibility fields
            uuid: userUuid,
            nickname: user.name || "",
            signin_type: account.type,
            signin_provider: account.provider,
            signin_openid: account.providerAccountId,
            signin_ip: await getClientIp(),
            
            // Status
            status: 'active' as const,
          };

          try {
            const savedUser = await saveUser(dbUser);

            // Get user subscription info using the UUID primary key
            const { getUserSubscriptionInfo } = await import("@/models/subscription");
            const { findUserByUuid } = await import("@/models/user");
            
            // Get the modern user record with UUID primary key
            const modernUser = savedUser.uuid ? await findUserByUuid(savedUser.uuid) : null;
            if (modernUser) {
              const subscriptionInfo = await getUserSubscriptionInfo(modernUser.id);

              token.user = {
                uuid: savedUser.uuid, // Keep legacy UUID for compatibility
                id: modernUser.id, // Add modern UUID primary key
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatar_url: savedUser.avatar_url,
                created_at: savedUser.created_at,
                subscription: subscriptionInfo.subscription,
                plan: subscriptionInfo.plan,
                credits_balance: subscriptionInfo.credits_balance,
                can_use_batch: subscriptionInfo.can_use_batch,
                subscription_tier: subscriptionInfo.plan.id,
              };
            } else {
              // Fallback to basic user info if modern user record not found
              token.user = {
                uuid: savedUser.uuid,
                email: savedUser.email,
                nickname: savedUser.nickname,
                avatar_url: savedUser.avatar_url,
                created_at: savedUser.created_at,
                subscription_tier: 'free',
                can_use_batch: false,
                credits_balance: 5,
              };
            }
          } catch (e) {
            console.error("save user failed:", e);
            // Fallback to basic user info
            token.user = {
              uuid: user.email, // Use email as fallback UUID
              email: user.email,
              nickname: user.name || "",
              avatar_url: user.image || "",
              created_at: new Date().toISOString(),
              subscription_tier: 'free',
              can_use_batch: false,
              credits_balance: 5,
            };
          }
        }
        return token;
      } catch (e) {
        console.error("jwt callback error:", e);
        return token;
      }
    },
  },
};
